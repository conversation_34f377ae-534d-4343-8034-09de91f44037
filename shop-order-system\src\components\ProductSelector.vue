<template>
  <div class="product-selector">
    <!-- 已选商品列表 -->
    <div class="selected-products" v-if="selectedProducts.length > 0">
      <h4>已选商品</h4>
      <div class="product-list">
        <div 
          v-for="(item, index) in selectedProducts" 
          :key="index"
          class="product-item"
        >
          <div class="product-info">
            <div class="product-name">{{ item.product?.name || '未知商品' }}</div>
            <div class="product-category">{{ item.product?.category || '未分类' }}</div>
          </div>
          
          <div class="product-attributes" v-if="item.selectedAttributes && Object.keys(item.selectedAttributes).length > 0">
            <el-tag 
              v-for="(value, key) in item.selectedAttributes" 
              :key="key"
              size="small"
              type="info"
            >
              {{ key }}: {{ value }}
            </el-tag>
          </div>
          
          <div class="product-controls">
            <div class="quantity-control">
              <el-input-number
                v-model="item.quantity"
                :min="1"
                :max="999"
                size="small"
                @change="updateQuantity(index, $event)"
              />
            </div>
            
            <div class="price-info">
              <span class="unit-price">¥{{ parseFloat(item.product?.price || 0).toFixed(2) }}</span>
              <span class="total-price">¥{{ (parseFloat(item.product?.price || 0) * item.quantity).toFixed(2) }}</span>
            </div>
            
            <el-button 
              type="danger" 
              size="small" 
              @click="removeProduct(index)"
              :icon="Delete"
            />
          </div>
        </div>
      </div>
      
      <!-- 总计 -->
      <div class="total-summary">
        <div class="total-info">
          <span class="total-label">总计：</span>
          <span class="total-amount">¥{{ totalAmount.toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <!-- 添加商品按钮 -->
    <div class="add-product-section">
      <el-button type="primary" @click="showProductDialog = true">
        <el-icon><Plus /></el-icon>
        添加商品
      </el-button>
    </div>

    <!-- 商品选择对话框 -->
    <el-dialog
      v-model="showProductDialog"
      title="选择商品"
      width="800px"
      @close="resetProductDialog"
    >
      <!-- 搜索和筛选 -->
      <div class="product-search">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input
              v-model="searchQuery"
              placeholder="搜索商品名称"
              prefix-icon="Search"
              clearable
              @input="filterProducts"
            />
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="categoryFilter"
              placeholder="选择分类"
              clearable
              @change="filterProducts"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-switch
              v-model="onlyListed"
              active-text="仅上架"
              @change="filterProducts"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 商品列表 -->
      <div class="product-grid">
        <div 
          v-for="product in filteredProducts" 
          :key="product.id"
          class="product-card"
          :class="{ 'selected': isProductSelected(product.id) }"
          @click="selectProduct(product)"
        >
          <div class="product-image">
            <img 
              v-if="product.image_url" 
              :src="product.image_url" 
              :alt="product.name"
              @error="handleImageError"
            />
            <div v-else class="no-image">
              <el-icon><Picture /></el-icon>
            </div>
          </div>
          
          <div class="product-details">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-category">{{ product.category }}</div>
            <div class="product-price">¥{{ parseFloat(product.price).toFixed(2) }}</div>
            <div class="product-status">
              <el-tag :type="product.is_listed ? 'success' : 'danger'" size="small">
                {{ product.is_listed ? '上架' : '下架' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 无商品提示 -->
      <div v-if="filteredProducts.length === 0" class="no-products">
        <el-empty description="没有找到匹配的商品" />
      </div>
    </el-dialog>

    <!-- 商品属性选择对话框 -->
    <el-dialog
      v-model="showAttributeDialog"
      title="选择商品属性"
      width="500px"
      @close="resetAttributeDialog"
    >
      <div v-if="selectedProductForAttribute">
        <div class="product-summary">
          <h4>{{ selectedProductForAttribute.name }}</h4>
          <p>价格: ¥{{ parseFloat(selectedProductForAttribute.price).toFixed(2) }}</p>
        </div>
        
        <el-form :model="attributeForm" label-width="80px">
          <el-form-item label="数量">
            <el-input-number
              v-model="attributeForm.quantity"
              :min="1"
              :max="999"
            />
          </el-form-item>
          
          <div 
            v-for="(options, attrName) in selectedProductForAttribute.custom_attributes" 
            :key="attrName"
          >
            <el-form-item :label="attrName">
              <el-select
                v-model="attributeForm.selectedAttributes[attrName]"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="option in options"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showAttributeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAddProduct">确认添加</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, Picture } from '@element-plus/icons-vue'
import { productAPI } from '../lib/api'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'total-change'])

// 状态
const selectedProducts = ref([...props.modelValue])
const showProductDialog = ref(false)
const showAttributeDialog = ref(false)
const products = ref([])
const searchQuery = ref('')
const categoryFilter = ref('')
const onlyListed = ref(true)
const selectedProductForAttribute = ref(null)
const isUpdatingFromProps = ref(false) // 防止循环更新的标志位

// 属性选择表单
const attributeForm = reactive({
  quantity: 1,
  selectedAttributes: {}
})

// 计算属性
const filteredProducts = computed(() => {
  return products.value.filter(product => {
    // 名称搜索
    if (searchQuery.value && !product.name.toLowerCase().includes(searchQuery.value.toLowerCase())) {
      return false
    }
    
    // 分类筛选
    if (categoryFilter.value && product.category !== categoryFilter.value) {
      return false
    }
    
    // 上架状态筛选
    if (onlyListed.value && !product.is_listed) {
      return false
    }
    
    return true
  })
})

const categories = computed(() => {
  const cats = [...new Set(products.value.map(p => p.category).filter(Boolean))]
  return cats.sort()
})

const totalAmount = computed(() => {
  return selectedProducts.value.reduce((total, item) => {
    return total + (parseFloat(item.product?.price || 0) * item.quantity)
  }, 0)
})

// 监听选中商品变化
watch(selectedProducts, (newVal) => {
  if (!isUpdatingFromProps.value) {
    emit('update:modelValue', newVal)
    emit('total-change', totalAmount.value)
  }
}, { deep: true })

// 监听外部传入的值变化，但避免循环更新
watch(() => props.modelValue, (newVal) => {
  isUpdatingFromProps.value = true
  nextTick(() => {
    selectedProducts.value = [...newVal]
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  })
}, { deep: true })

// 方法
const loadProducts = async () => {
  try {
    const result = await productAPI.getProducts()
    if (result.success) {
      products.value = result.data
    }
  } catch (error) {
    console.error('加载商品失败:', error)
    ElMessage.error('加载商品失败')
  }
}

const filterProducts = () => {
  // 触发计算属性重新计算
}

const isProductSelected = (productId) => {
  return selectedProducts.value.some(item => item.product?.id === productId)
}

const selectProduct = (product) => {
  if (!product.is_listed) {
    ElMessage.warning('该商品已下架，无法选择')
    return
  }
  
  selectedProductForAttribute.value = product
  
  // 重置属性表单
  attributeForm.quantity = 1
  attributeForm.selectedAttributes = {}
  
  // 如果商品有自定义属性，显示属性选择对话框
  if (product.custom_attributes && Object.keys(product.custom_attributes).length > 0) {
    showAttributeDialog.value = true
  } else {
    // 直接添加商品
    confirmAddProduct()
  }
}

const confirmAddProduct = () => {
  if (!selectedProductForAttribute.value) return

  const newItem = {
    product: selectedProductForAttribute.value,
    quantity: attributeForm.quantity,
    selectedAttributes: { ...attributeForm.selectedAttributes }
  }

  selectedProducts.value.push(newItem)

  // 使用nextTick确保DOM更新完成后再关闭对话框
  nextTick(() => {
    showAttributeDialog.value = false
    showProductDialog.value = false
    ElMessage.success('商品添加成功')
  })
}

const updateQuantity = (index, quantity) => {
  if (quantity && quantity > 0) {
    selectedProducts.value[index].quantity = quantity
  }
}

const removeProduct = (index) => {
  selectedProducts.value.splice(index, 1)
  ElMessage.success('商品已移除')
}

const resetProductDialog = () => {
  searchQuery.value = ''
  categoryFilter.value = ''
}

const resetAttributeDialog = () => {
  selectedProductForAttribute.value = null
  attributeForm.quantity = 1
  attributeForm.selectedAttributes = {}
}

const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 组件挂载时加载商品
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.product-selector {
  width: 100%;
}

.selected-products {
  margin-bottom: 20px;
}

.selected-products h4 {
  margin-bottom: 12px;
  color: #303133;
}

.product-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  flex: 1;
  margin-right: 12px;
}

.product-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.product-category {
  font-size: 12px;
  color: #909399;
}

.product-attributes {
  margin-right: 12px;
}

.product-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 80px;
}

.unit-price {
  font-size: 12px;
  color: #909399;
}

.total-price {
  font-weight: bold;
  color: #409eff;
}

.total-summary {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 12px;
}

.total-info {
  text-align: right;
}

.total-label {
  font-size: 16px;
  color: #303133;
}

.total-amount {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin-left: 8px;
}

.add-product-section {
  text-align: center;
  padding: 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.product-search {
  margin-bottom: 20px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.product-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.product-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.product-card.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.product-image {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.no-image {
  color: #c0c4cc;
  font-size: 24px;
}

.product-details {
  text-align: center;
}

.product-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.product-category {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.no-products {
  text-align: center;
  padding: 40px;
}

.product-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.product-summary h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.product-summary p {
  margin: 0;
  color: #409eff;
  font-weight: bold;
}
</style>
