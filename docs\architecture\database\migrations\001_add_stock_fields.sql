-- 数据库迁移脚本：添加库存字段到商品表
-- 执行时间：2024年

-- 1. 为商品表添加库存相关字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS stock_quantity INTEGER DEFAULT 0 NOT NULL,
ADD COLUMN IF NOT EXISTS min_stock_level INTEGER DEFAULT 10 NOT NULL,
ADD COLUMN IF NOT EXISTS max_stock_level INTEGER DEFAULT 1000 NOT NULL,
ADD COLUMN IF NOT EXISTS stock_unit VARCHAR(50) DEFAULT '件';

-- 2. 创建库存相关索引
CREATE INDEX IF NOT EXISTS idx_products_stock_quantity ON products(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_products_low_stock ON products(stock_quantity, min_stock_level);

-- 3. 为现有商品设置默认库存值
UPDATE products 
SET 
    stock_quantity = 100,
    min_stock_level = 10,
    max_stock_level = 1000,
    stock_unit = '件'
WHERE stock_quantity IS NULL OR stock_quantity = 0;

-- 4. 添加节假日表（如果不存在）
CREATE TABLE IF NOT EXISTS holidays (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    name TEXT NOT NULL,
    type TEXT CHECK (type IN ('holiday', 'workday')) NOT NULL,
    is_official BOOLEAN DEFAULT true,
    year INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建节假日表索引
CREATE INDEX IF NOT EXISTS idx_holidays_date ON holidays(date);
CREATE INDEX IF NOT EXISTS idx_holidays_year ON holidays(year);
CREATE INDEX IF NOT EXISTS idx_holidays_type ON holidays(type);
CREATE UNIQUE INDEX IF NOT EXISTS idx_holidays_date_unique ON holidays(date);

-- 6. 添加订单状态历史表（如果不存在）
CREATE TABLE IF NOT EXISTS order_status_history (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    from_status TEXT,
    to_status TEXT NOT NULL,
    changed_by TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 创建订单状态历史表索引
CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_created_at ON order_status_history(created_at);

-- 8. 确保地址表有正确的约束
-- 检查是否存在客户默认地址的唯一约束
DO $$
BEGIN
    -- 为每个客户确保只有一个默认地址
    UPDATE addresses 
    SET is_default = false 
    WHERE id NOT IN (
        SELECT DISTINCT ON (customer_id) id 
        FROM addresses 
        WHERE is_default = true 
        ORDER BY customer_id, created_at DESC
    ) AND is_default = true;
    
    -- 为没有默认地址的客户设置第一个地址为默认
    UPDATE addresses 
    SET is_default = true 
    WHERE id IN (
        SELECT DISTINCT ON (customer_id) id 
        FROM addresses a1
        WHERE NOT EXISTS (
            SELECT 1 FROM addresses a2 
            WHERE a2.customer_id = a1.customer_id 
            AND a2.is_default = true
        )
        ORDER BY customer_id, created_at ASC
    );
END $$;

-- 9. 添加触发器确保每个客户只有一个默认地址
CREATE OR REPLACE FUNCTION ensure_single_default_address()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果新插入或更新的地址设为默认
    IF NEW.is_default = true THEN
        -- 将该客户的其他地址设为非默认
        UPDATE addresses 
        SET is_default = false 
        WHERE customer_id = NEW.customer_id 
        AND id != NEW.id 
        AND is_default = true;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_ensure_single_default_address ON addresses;
CREATE TRIGGER trigger_ensure_single_default_address
    BEFORE INSERT OR UPDATE ON addresses
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_address();

-- 10. 验证数据完整性
DO $$
DECLARE
    customer_count INTEGER;
    address_count INTEGER;
    default_address_count INTEGER;
    product_count INTEGER;
    low_stock_count INTEGER;
BEGIN
    -- 统计客户数量
    SELECT COUNT(*) INTO customer_count FROM customers;
    
    -- 统计地址数量
    SELECT COUNT(*) INTO address_count FROM addresses;
    
    -- 统计默认地址数量
    SELECT COUNT(*) INTO default_address_count FROM addresses WHERE is_default = true;
    
    -- 统计商品数量
    SELECT COUNT(*) INTO product_count FROM products;
    
    -- 统计低库存商品数量
    SELECT COUNT(*) INTO low_stock_count FROM products WHERE stock_quantity <= min_stock_level;
    
    -- 输出统计信息
    RAISE NOTICE '数据迁移完成统计:';
    RAISE NOTICE '客户总数: %', customer_count;
    RAISE NOTICE '地址总数: %', address_count;
    RAISE NOTICE '默认地址数: %', default_address_count;
    RAISE NOTICE '商品总数: %', product_count;
    RAISE NOTICE '低库存商品数: %', low_stock_count;
    
    -- 验证数据完整性
    IF default_address_count > customer_count THEN
        RAISE WARNING '发现数据异常：默认地址数量超过客户数量';
    END IF;
    
    IF EXISTS (
        SELECT customer_id 
        FROM addresses 
        WHERE is_default = true 
        GROUP BY customer_id 
        HAVING COUNT(*) > 1
    ) THEN
        RAISE WARNING '发现数据异常：存在客户有多个默认地址';
    END IF;
END $$;

-- 11. 创建数据备份表（可选）
CREATE TABLE IF NOT EXISTS migration_backup_products AS 
SELECT * FROM products WHERE 1=0;

CREATE TABLE IF NOT EXISTS migration_backup_addresses AS 
SELECT * FROM addresses WHERE 1=0;

-- 备份关键数据
INSERT INTO migration_backup_products 
SELECT * FROM products 
WHERE stock_quantity IS NULL OR stock_quantity = 0;

INSERT INTO migration_backup_addresses 
SELECT * FROM addresses 
WHERE is_default = true;

-- 12. 添加迁移记录
CREATE TABLE IF NOT EXISTS migration_history (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    success BOOLEAN DEFAULT true,
    notes TEXT
);

INSERT INTO migration_history (migration_name, notes) 
VALUES ('001_add_stock_fields', '添加商品库存字段、节假日表、订单状态历史表，修复地址默认设置');

COMMIT;
