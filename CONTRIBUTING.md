# 贡献指南

感谢您对小商店订单管理系统的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能

## 🚀 快速开始

### 1. Fork 项目

点击页面右上角的 "Fork" 按钮，将项目复制到您的GitHub账户。

### 2. 克隆项目

```bash
git clone https://github.com/YOUR_USERNAME/gglist.git
cd gglist
```

### 3. 设置开发环境

详细的开发环境设置请参考：[开发指导文档](docs/development/开发指导文档.md)

### 4. 创建分支

```bash
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/your-bug-fix
```

## 📋 贡献类型

### 🐛 Bug 报告

在提交Bug报告前，请：

1. **搜索现有Issues** - 确认问题尚未被报告
2. **使用最新版本** - 确保在最新版本中仍存在问题
3. **提供详细信息** - 包含重现步骤、环境信息等

**Bug报告模板：**
```markdown
## Bug描述
简要描述遇到的问题

## 重现步骤
1. 进入 '...'
2. 点击 '...'
3. 滚动到 '...'
4. 看到错误

## 期望行为
描述您期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统: [例如 Windows 10]
- 浏览器: [例如 Chrome 91]
- 版本: [例如 v1.0.0]

## 附加信息
添加任何其他相关信息、截图等
```

### 💡 功能建议

提交功能建议时，请：

1. **描述问题** - 说明当前的痛点
2. **提出解决方案** - 详细描述建议的功能
3. **考虑替代方案** - 提供其他可能的解决方案
4. **评估影响** - 考虑对现有功能的影响

### 📝 文档改进

文档贡献包括：

- 修正错别字和语法错误
- 改进现有文档的清晰度
- 添加缺失的文档
- 翻译文档

### 🔧 代码贡献

#### 代码规范

**前端 (Vue 3)**
- 遵循 [Vue 3 风格指南](https://vuejs.org/style-guide/)
- 使用 ESLint 和 Prettier 进行代码格式化
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

**后端 (Go)**
- 遵循 [Go 代码规范](https://golang.org/doc/effective_go.html)
- 使用 `gofmt` 格式化代码
- 函数和变量命名使用驼峰命名法
- 包名使用小写字母

#### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型说明：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(order): 添加订单批量导出功能

- 支持Excel格式导出
- 支持日期范围筛选
- 添加导出进度提示

Closes #123
```

#### 代码审查

所有代码贡献都需要通过代码审查：

1. **自我审查** - 提交前仔细检查代码
2. **测试覆盖** - 确保新代码有相应测试
3. **文档更新** - 更新相关文档
4. **CI检查** - 确保所有自动化检查通过

## 🧪 测试

### 运行测试

```bash
# 前端测试
cd shop-order-system
npm run test

# 后端测试
cd backend
go test ./...
```

### 测试要求

- 新功能必须包含测试用例
- Bug修复应包含回归测试
- 测试覆盖率不应降低

## 📦 发布流程

### 版本管理

项目使用 [语义化版本](https://semver.org/lang/zh-CN/)：

- `主版本号`: 不兼容的API修改
- `次版本号`: 向下兼容的功能性新增
- `修订号`: 向下兼容的问题修正

### 发布步骤

1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建 Release Tag
4. 发布 Release Notes

## 🤝 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境：

- **尊重他人** - 尊重不同的观点和经验
- **建设性沟通** - 提供有建设性的反馈
- **包容性** - 欢迎所有背景的贡献者
- **专业性** - 保持专业和礼貌的交流

### 沟通渠道

- **GitHub Issues** - Bug报告和功能建议
- **GitHub Discussions** - 一般讨论和问答
- **Pull Requests** - 代码审查和讨论

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 提交新的 [Issue](../../issues/new)
4. 参与 [Discussions](../../discussions)

## 🏆 贡献者认可

我们会在以下地方认可贡献者：

- README.md 中的贡献者列表
- Release Notes 中的感谢
- 项目网站的贡献者页面

## 📄 许可证

通过贡献代码，您同意您的贡献将在 [MIT许可证](LICENSE) 下授权。

---

**🙏 再次感谢您的贡献！每一个贡献都让这个项目变得更好。**
