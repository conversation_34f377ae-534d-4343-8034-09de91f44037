# 项目结构说明

本文档详细说明了小商店订单管理系统的项目结构和各个目录的作用。

## 📁 总体结构

```
gglist/                                 # 项目根目录
├── backend/                           # Go 后端服务
├── shop-order-system/                 # Vue 前端应用
├── docs/                             # 项目文档
├── scripts/                          # 脚本文件
├── docker-compose.yml                # Docker 编排文件
├── README.md                         # 项目主文档
├── LICENSE                           # 开源许可证
├── CHANGELOG.md                      # 版本变更记录
├── CONTRIBUTING.md                   # 贡献指南
├── PROJECT_STATUS.md                 # 项目状态报告
└── .gitignore                        # Git 忽略文件
```

## 🔧 后端结构 (backend/)

```
backend/
├── internal/                         # 内部包（不对外暴露）
│   ├── config/                      # 配置管理
│   │   └── config.go               # 配置结构和加载
│   ├── controllers/                 # 控制器层
│   │   ├── auth.go                 # 认证控制器
│   │   ├── customer.go             # 客户管理控制器
│   │   ├── address.go              # 地址管理控制器
│   │   ├── product.go              # 商品管理控制器
│   │   ├── order.go                # 订单管理控制器
│   │   ├── user.go                 # 用户管理控制器
│   │   ├── task.go                 # 定时任务控制器
│   │   └── dashboard.go            # 仪表盘控制器
│   ├── database/                    # 数据库相关
│   │   └── database.go             # 数据库连接和初始化
│   ├── middleware/                  # 中间件
│   │   ├── auth.go                 # JWT认证中间件
│   │   ├── cors.go                 # CORS中间件
│   │   └── logger.go               # 日志中间件
│   ├── models/                      # 数据模型
│   │   ├── user.go                 # 用户模型
│   │   ├── customer.go             # 客户模型
│   │   ├── address.go              # 地址模型
│   │   ├── product.go              # 商品模型
│   │   ├── order.go                # 订单模型
│   │   └── task.go                 # 任务模型
│   ├── router/                      # 路由配置
│   │   └── router.go               # 路由定义
│   ├── services/                    # 业务逻辑层
│   │   ├── auth_service.go         # 认证服务
│   │   ├── order_service.go        # 订单服务
│   │   └── task_service.go         # 任务服务
│   └── utils/                       # 工具函数
│       ├── jwt.go                  # JWT工具
│       ├── response.go             # 响应工具
│       └── validator.go            # 验证工具
├── data/                            # 数据文件
│   └── shop_order.db               # SQLite数据库文件
├── uploads/                         # 文件上传目录
├── Dockerfile                       # Docker构建文件
├── create_admin.go                  # 创建管理员脚本
├── go.mod                          # Go模块定义
├── go.sum                          # Go依赖校验
└── main.go                         # 应用入口文件
```

### 后端架构说明

- **分层架构**: 采用经典的MVC架构模式
- **依赖注入**: 使用接口和依赖注入提高可测试性
- **中间件**: 使用Gin中间件处理横切关注点
- **数据访问**: 使用XORM作为ORM框架

## 🎨 前端结构 (shop-order-system/)

```
shop-order-system/
├── src/                             # 源代码目录
│   ├── assets/                     # 静态资源
│   │   ├── css/                   # 样式文件
│   │   ├── images/                # 图片资源
│   │   └── icons/                 # 图标资源
│   ├── components/                 # 可复用组件
│   │   ├── common/                # 通用组件
│   │   │   ├── AppHeader.vue      # 应用头部
│   │   │   ├── AppSidebar.vue     # 侧边栏
│   │   │   └── AppFooter.vue      # 应用底部
│   │   ├── forms/                 # 表单组件
│   │   │   ├── CustomerForm.vue   # 客户表单
│   │   │   ├── ProductForm.vue    # 商品表单
│   │   │   └── OrderForm.vue      # 订单表单
│   │   ├── ProductSelector.vue     # 商品选择器
│   │   ├── AddressManager.vue      # 地址管理器
│   │   └── Calendar.vue           # 日历组件
│   ├── views/                      # 页面组件
│   │   ├── auth/                  # 认证相关页面
│   │   │   └── LoginView.vue      # 登录页面
│   │   ├── DashboardView.vue      # 仪表盘
│   │   ├── CustomersView.vue      # 客户管理
│   │   ├── ProductsView.vue       # 商品管理
│   │   ├── OrdersView.vue         # 订单管理
│   │   ├── CalendarView.vue       # 生产日历
│   │   ├── UsersView.vue          # 用户管理
│   │   └── TasksView.vue          # 定时任务
│   ├── stores/                     # 状态管理
│   │   ├── auth.js                # 认证状态
│   │   ├── customers.js           # 客户状态
│   │   ├── products.js            # 商品状态
│   │   ├── orders.js              # 订单状态
│   │   ├── users.js               # 用户状态
│   │   └── tasks.js               # 任务状态
│   ├── router/                     # 路由配置
│   │   └── index.js               # 路由定义
│   ├── lib/                        # 工具库
│   │   ├── api.js                 # API调用封装
│   │   ├── supabase.js            # Supabase配置
│   │   ├── utils.js               # 通用工具函数
│   │   ├── constants.js           # 常量定义
│   │   └── demo-data.js           # 演示数据
│   ├── App.vue                     # 根组件
│   └── main.js                     # 应用入口
├── public/                          # 公共资源
│   ├── favicon.ico                 # 网站图标
│   └── index.html                  # HTML模板
├── Dockerfile                       # Docker构建文件
├── nginx.conf                       # Nginx配置
├── package.json                     # 项目依赖
├── package-lock.json               # 依赖锁定文件
├── vite.config.js                  # Vite配置
├── eslint.config.js                # ESLint配置
├── jsconfig.json                   # JavaScript配置
└── .env                            # 环境变量
```

### 前端架构说明

- **组件化**: 采用Vue 3组件化开发
- **状态管理**: 使用Pinia进行状态管理
- **路由管理**: 使用Vue Router进行页面路由
- **构建工具**: 使用Vite作为构建工具

## 📚 文档结构 (docs/)

```
docs/
├── api/                             # API文档
│   └── README.md                   # API接口文档
├── architecture/                    # 架构文档
│   ├── database/                   # 数据库设计
│   │   ├── schema.sql             # 数据库结构
│   │   └── README.md              # 数据库说明
│   └── PROJECT_STRUCTURE.md       # 项目结构说明
├── deployment/                      # 部署文档
│   └── README-本地启动.md          # 本地部署指南
├── development/                     # 开发文档
│   ├── tests/                     # 测试文档
│   ├── 开发指导文档.md             # 开发指南
│   ├── 开发计划.md                 # 开发计划
│   └── 项目开发完成总结_2025-06-17.md # 开发总结
├── user-guide/                      # 用户指南
│   └── 快速开始指南.md             # 快速开始
└── README.md                       # 文档索引
```

## 🛠️ 脚本目录 (scripts/)

```
scripts/
├── start-dev.bat                    # Windows开发环境启动脚本
└── start-dev.sh                     # Linux/Mac开发环境启动脚本
```

## 🐳 容器化文件

- **docker-compose.yml**: Docker Compose编排文件
- **backend/Dockerfile**: 后端Docker构建文件
- **shop-order-system/Dockerfile**: 前端Docker构建文件
- **shop-order-system/nginx.conf**: Nginx配置文件

## 📝 配置文件

### 环境配置
- **shop-order-system/.env**: 前端环境变量
- **backend/.env**: 后端环境变量（可选）

### 代码质量
- **shop-order-system/eslint.config.js**: ESLint配置
- **shop-order-system/jsconfig.json**: JavaScript配置
- **.gitignore**: Git忽略文件配置

### 构建配置
- **shop-order-system/vite.config.js**: Vite构建配置
- **shop-order-system/package.json**: 前端依赖配置
- **backend/go.mod**: Go模块配置

## 🔍 目录命名规范

### 后端命名规范
- **包名**: 小写字母，使用下划线分隔
- **文件名**: 小写字母，使用下划线分隔
- **结构体**: 大驼峰命名法 (PascalCase)
- **函数**: 大驼峰命名法 (PascalCase)
- **变量**: 小驼峰命名法 (camelCase)

### 前端命名规范
- **组件名**: 大驼峰命名法 (PascalCase)
- **文件名**: 大驼峰命名法 (PascalCase) 或 kebab-case
- **变量**: 小驼峰命名法 (camelCase)
- **常量**: 大写字母，使用下划线分隔

## 📋 文件作用说明

### 关键文件
- **main.go**: 后端应用入口，初始化服务器
- **main.js**: 前端应用入口，初始化Vue应用
- **router.go**: 后端路由配置
- **index.js**: 前端路由配置
- **database.go**: 数据库连接和初始化
- **api.js**: 前端API调用封装

### 配置文件
- **docker-compose.yml**: 容器编排配置
- **vite.config.js**: 前端构建配置
- **go.mod**: Go依赖管理
- **package.json**: Node.js依赖管理

---

**📝 注意：此文档会随着项目结构的变化而更新，请保持关注最新版本。**
