@echo off
chcp 65001 >nul
echo ========================================
echo Shop Order System - Development Startup Script
echo ========================================
echo.

echo [1/3] Checking environment...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Node.js not found, please install Node.js 16+
    pause
    exit /b 1
)

where go >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Go not found, please install Go 1.19+
    pause
    exit /b 1
)

echo [2/3] Starting backend service...
cd /d "%~dp0.."
cd backend
start "Backend Service" cmd /k "go run main.go"

echo [3/3] Starting frontend service...
cd /d "%~dp0.."
cd shop-order-system
start "Frontend Service" cmd /k "npm run dev"

echo.
echo ========================================
echo Startup completed!
echo Frontend: http://localhost:5173
echo Backend: http://localhost:8080
echo ========================================
echo.
echo Press any key to close this window...
pause >nul
