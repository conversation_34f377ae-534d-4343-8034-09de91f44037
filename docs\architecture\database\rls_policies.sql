-- 行级安全策略 (Row Level Security Policies)
-- 为小商店订单管理系统配置数据访问权限

-- 启用RLS
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 创建辅助函数来获取当前用户角色
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM user_profiles 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建辅助函数来检查用户是否为超级管理员
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_user_role() = 'super_admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建辅助函数来检查用户是否已认证且活跃
CREATE OR REPLACE FUNCTION is_authenticated_and_active()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 
            FROM user_profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 商品表策略
-- 所有认证用户可以查看上架商品，管理员可以查看所有商品
CREATE POLICY "商品查看策略" ON products
    FOR SELECT
    USING (
        is_authenticated_and_active() AND 
        (is_listed = true OR is_super_admin() OR get_user_role() = 'staff')
    );

-- 员工和管理员可以插入商品
CREATE POLICY "商品插入策略" ON products
    FOR INSERT
    WITH CHECK (is_authenticated_and_active());

-- 员工和管理员可以更新商品
CREATE POLICY "商品更新策略" ON products
    FOR UPDATE
    USING (is_authenticated_and_active());

-- 只有超级管理员可以删除商品
CREATE POLICY "商品删除策略" ON products
    FOR DELETE
    USING (is_super_admin());

-- 客户表策略
-- 所有认证用户可以查看、插入、更新客户信息
CREATE POLICY "客户全权限策略" ON customers
    FOR ALL
    USING (is_authenticated_and_active())
    WITH CHECK (is_authenticated_and_active());

-- 地址表策略
-- 所有认证用户可以管理地址信息
CREATE POLICY "地址全权限策略" ON addresses
    FOR ALL
    USING (is_authenticated_and_active())
    WITH CHECK (is_authenticated_and_active());

-- 订单表策略
-- 所有认证用户可以管理订单
CREATE POLICY "订单全权限策略" ON orders
    FOR ALL
    USING (is_authenticated_and_active())
    WITH CHECK (is_authenticated_and_active());

-- 订单商品关联表策略
-- 所有认证用户可以管理订单商品
CREATE POLICY "订单商品全权限策略" ON order_items
    FOR ALL
    USING (is_authenticated_and_active())
    WITH CHECK (is_authenticated_and_active());

-- 用户档案表策略
-- 用户可以查看自己的档案，超级管理员可以查看所有档案
CREATE POLICY "用户档案查看策略" ON user_profiles
    FOR SELECT
    USING (
        auth.uid() = id OR is_super_admin()
    );

-- 用户可以更新自己的档案，超级管理员可以更新所有档案
CREATE POLICY "用户档案更新策略" ON user_profiles
    FOR UPDATE
    USING (
        auth.uid() = id OR is_super_admin()
    )
    WITH CHECK (
        auth.uid() = id OR is_super_admin()
    );

-- 只有超级管理员可以插入新用户档案
CREATE POLICY "用户档案插入策略" ON user_profiles
    FOR INSERT
    WITH CHECK (is_super_admin());

-- 只有超级管理员可以删除用户档案
CREATE POLICY "用户档案删除策略" ON user_profiles
    FOR DELETE
    USING (is_super_admin());

-- 创建触发器函数，在用户注册时自动创建用户档案
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, role)
    VALUES (NEW.id, NEW.email, 'staff');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器，在auth.users表插入新用户时自动创建档案
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
