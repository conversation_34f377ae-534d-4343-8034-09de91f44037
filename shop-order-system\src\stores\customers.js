import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { customerAPI, addressAPI } from '../lib/api'

export const useCustomersStore = defineStore('customers', () => {
  // 状态
  const customers = ref([])
  const loading = ref(false)

  // 分页相关状态
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const hasMore = ref(true)

  // 缓存相关状态
  const cache = ref(new Map())
  const cacheExpiry = ref(new Map())
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // 缓存辅助方法
  const getCacheKey = (params = {}) => {
    return JSON.stringify({ ...params, page: currentPage.value, pageSize: pageSize.value })
  }

  const isValidCache = (key) => {
    const expiry = cacheExpiry.value.get(key)
    return expiry && Date.now() < expiry
  }

  const setCache = (key, data) => {
    cache.value.set(key, data)
    cacheExpiry.value.set(key, Date.now() + CACHE_DURATION)
  }

  const getCache = (key) => {
    if (isValidCache(key)) {
      return cache.value.get(key)
    }
    return null
  }

  const clearCache = () => {
    cache.value.clear()
    cacheExpiry.value.clear()
  }

  // 获取客户列表（包含地址信息，支持分页和缓存）
  const fetchCustomers = async (params = {}, useCache = true) => {
    try {
      loading.value = true

      // 构建缓存键
      const cacheKey = getCacheKey(params)

      // 检查缓存
      if (useCache) {
        const cachedData = getCache(cacheKey)
        if (cachedData) {
          customers.value = cachedData.customers
          total.value = cachedData.total
          hasMore.value = cachedData.hasMore
          return { success: true, data: cachedData.customers, fromCache: true }
        }
      }

      // 构建查询参数
      const queryParams = {
        page: currentPage.value,
        limit: pageSize.value,
        ...params
      }

      const result = await customerAPI.getCustomers(queryParams)

      if (result.success) {
        const newCustomers = result.data || []

        // 如果是第一页，替换数据；否则追加数据
        if (currentPage.value === 1) {
          customers.value = newCustomers
        } else {
          customers.value = [...customers.value, ...newCustomers]
        }

        total.value = result.total || newCustomers.length
        hasMore.value = newCustomers.length === pageSize.value

        // 缓存数据
        if (useCache) {
          setCache(cacheKey, {
            customers: customers.value,
            total: total.value,
            hasMore: hasMore.value
          })
        }

        return { success: true, data: newCustomers }
      } else {
        throw new Error(result.error || '获取客户列表失败')
      }

    } catch (error) {
      console.error('获取客户列表失败:', error)
      ElMessage.error('获取客户列表失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 加载更多客户
  const loadMoreCustomers = async (params = {}) => {
    if (!hasMore.value || loading.value) {
      return { success: false, message: '没有更多数据或正在加载中' }
    }

    currentPage.value += 1
    return await fetchCustomers(params, false) // 加载更多时不使用缓存
  }

  // 刷新客户列表
  const refreshCustomers = async (params = {}) => {
    currentPage.value = 1
    clearCache()
    return await fetchCustomers(params, false)
  }

  // 重置分页状态
  const resetPagination = () => {
    currentPage.value = 1
    hasMore.value = true
    total.value = 0
  }

  // 创建客户
  const createCustomer = async (customerData) => {
    try {
      loading.value = true

      const result = await customerAPI.createCustomer(customerData)

      if (result.success) {
        // 添加空的地址数组
        result.data.addresses = []
        customers.value.unshift(result.data)

        ElMessage.success('客户创建成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error)
      }

    } catch (error) {
      console.error('创建客户失败:', error)
      ElMessage.error('创建客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新客户
  const updateCustomer = async (id, updates) => {
    try {
      loading.value = true

      const result = await customerAPI.updateCustomer(id, updates)

      if (result.success) {
        const index = customers.value.findIndex(c => c.id === id)
        if (index > -1) {
          // 保留地址信息
          result.data.addresses = customers.value[index].addresses
          customers.value[index] = result.data
        }
        clearCache() // 清除缓存以确保数据一致性
        ElMessage.success('客户信息更新成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '更新客户失败')
      }

    } catch (error) {
      console.error('更新客户失败:', error)
      ElMessage.error('更新客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 删除客户
  const deleteCustomer = async (id) => {
    try {
      loading.value = true

      const result = await customerAPI.deleteCustomer(id)

      if (result.success) {
        const index = customers.value.findIndex(c => c.id === id)
        if (index > -1) {
          customers.value.splice(index, 1)
          total.value -= 1
        }
        clearCache() // 清除缓存以确保数据一致性
        ElMessage.success('客户删除成功')
        return { success: true }
      } else {
        throw new Error(result.error || '删除客户失败')
      }

    } catch (error) {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 添加地址
  const addAddress = async (customerId, addressData) => {
    try {
      const result = await addressAPI.createAddress(customerId, addressData)

      if (result.success) {
        // 更新本地数据
        const customer = customers.value.find(c => c.id === customerId)
        if (customer) {
          if (!customer.addresses) customer.addresses = []
          customer.addresses.push(result.data)
        }
        clearCache() // 清除缓存
        ElMessage.success('地址添加成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '添加地址失败')
      }

    } catch (error) {
      console.error('添加地址失败:', error)
      ElMessage.error('添加地址失败')
      return { success: false, error }
    }
  }

  // 更新地址
  const updateAddress = async (addressId, updates) => {
    try {
      const result = await addressAPI.updateAddress(addressId, updates)

      if (result.success) {
        // 更新本地数据
        customers.value.forEach(customer => {
          if (customer.addresses) {
            const addressIndex = customer.addresses.findIndex(a => a.id === addressId)
            if (addressIndex > -1) {
              customer.addresses[addressIndex] = result.data
            }
          }
        })
        clearCache() // 清除缓存
        ElMessage.success('地址更新成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '更新地址失败')
      }

    } catch (error) {
      console.error('更新地址失败:', error)
      ElMessage.error('更新地址失败')
      return { success: false, error }
    }
  }

  // 删除地址
  const deleteAddress = async (addressId) => {
    try {
      const result = await addressAPI.deleteAddress(addressId)

      if (result.success) {
        // 更新本地数据
        customers.value.forEach(customer => {
          if (customer.addresses) {
            const addressIndex = customer.addresses.findIndex(a => a.id === addressId)
            if (addressIndex > -1) {
              customer.addresses.splice(addressIndex, 1)
            }
          }
        })
        clearCache() // 清除缓存
        ElMessage.success('地址删除成功')
        return { success: true }
      } else {
        throw new Error(result.error || '删除地址失败')
      }

    } catch (error) {
      console.error('删除地址失败:', error)
      ElMessage.error('删除地址失败')
      return { success: false, error }
    }
  }

  // 设置默认地址
  const setDefaultAddress = async (customerId, addressId) => {
    try {
      const result = await addressAPI.setDefaultAddress(customerId, addressId)

      if (result.success) {
        // 更新本地数据
        const customer = customers.value.find(c => c.id === customerId)
        if (customer && customer.addresses) {
          customer.addresses.forEach(addr => {
            addr.is_default = addr.id === addressId
          })
        }
        clearCache() // 清除缓存
        ElMessage.success('默认地址设置成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '设置默认地址失败')
      }

    } catch (error) {
      console.error('设置默认地址失败:', error)
      ElMessage.error('设置默认地址失败')
      return { success: false, error }
    }
  }

  // 搜索客户
  const searchCustomers = (query) => {
    if (!query) return customers.value
    
    const searchTerm = query.toLowerCase()
    return customers.value.filter(customer => 
      customer.name.toLowerCase().includes(searchTerm) ||
      (customer.contact && customer.contact.toLowerCase().includes(searchTerm))
    )
  }

  // 根据ID获取客户
  const getCustomerById = (id) => {
    return customers.value.find(c => c.id === id)
  }

  // 获取单个客户详情（从API）
  const getCustomer = async (id) => {
    try {
      const result = await customerAPI.getCustomer(id)
      if (result.success) {
        // 更新本地缓存中的客户信息
        const existingIndex = customers.value.findIndex(c => c.id === result.data.id)
        if (existingIndex >= 0) {
          customers.value[existingIndex] = result.data
        } else {
          customers.value.unshift(result.data)
        }
      }
      return result
    } catch (error) {
      console.error('获取客户详情失败:', error)
      return { success: false, error }
    }
  }

  // 获取客户的默认地址
  const getDefaultAddress = (customerId) => {
    const customer = getCustomerById(customerId)
    return customer?.addresses?.find(addr => addr.is_default)
  }

  return {
    // 状态
    customers,
    loading,
    currentPage,
    pageSize,
    total,
    hasMore,

    // 方法
    fetchCustomers,
    loadMoreCustomers,
    refreshCustomers,
    resetPagination,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    searchCustomers,
    getCustomer,
    getCustomerById,
    getDefaultAddress,
    clearCache
  }
})
