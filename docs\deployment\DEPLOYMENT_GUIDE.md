# 部署指南

本文档提供了小商店订单管理系统的完整部署指南，包括本地开发、测试环境和生产环境的部署方法。

## 📋 部署概览

| 部署方式 | 适用场景 | 复杂度 | 推荐指数 |
|----------|----------|--------|----------|
| 🔧 **本地开发** | 开发调试 | ⭐ | ⭐⭐⭐⭐⭐ |
| 🐳 **Docker部署** | 快速部署 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 🏭 **生产环境** | 正式上线 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| ☁️ **云端部署** | 云服务器 | ⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🔧 本地开发部署

### 环境要求

| 软件 | 版本要求 | 下载地址 |
|------|----------|----------|
| **Node.js** | 16.0+ | [nodejs.org](https://nodejs.org/) |
| **Go** | 1.19+ | [golang.org](https://golang.org/) |
| **Git** | 2.0+ | [git-scm.com](https://git-scm.com/) |

### 快速启动

#### 方式一：使用启动脚本（推荐）

**Windows:**
```bash
# 双击运行或在命令行执行
scripts\start-dev.bat
```

**Linux/Mac:**
```bash
# 给脚本执行权限
chmod +x scripts/start-dev.sh

# 运行脚本
./scripts/start-dev.sh
```

#### 方式二：手动启动

**1. 启动后端服务**
```bash
cd backend
go mod tidy
go run main.go
```

**2. 启动前端服务**
```bash
cd shop-order-system
npm install
npm run dev
```

### 访问地址

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/api/health

### 默认账户

| 角色 | 邮箱 | 密码 |
|------|------|------|
| 管理员 | <EMAIL> | demo123 |
| 员工 | <EMAIL> | demo123 |

## 🐳 Docker 部署

### 环境要求

- **Docker** 20.0+
- **Docker Compose** 2.0+

### 快速部署

#### 使用 SQLite（推荐用于开发/测试）

```bash
# 克隆项目
git clone <repository-url>
cd gglist

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 使用 MySQL（推荐用于生产）

```bash
# 启动包含MySQL的完整服务
docker-compose --profile mysql up -d

# 等待MySQL初始化完成
docker-compose logs mysql

# 检查服务状态
docker-compose ps
```

### 服务访问

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **MySQL数据库**: localhost:3306

### Docker 管理命令

```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f [service-name]

# 进入容器
docker-compose exec backend sh
docker-compose exec frontend sh

# 清理数据（谨慎使用）
docker-compose down -v
```

## 🏭 生产环境部署

### 环境准备

#### 服务器要求

| 配置项 | 最低要求 | 推荐配置 |
|--------|----------|----------|
| **CPU** | 1核 | 2核+ |
| **内存** | 1GB | 2GB+ |
| **存储** | 10GB | 20GB+ |
| **网络** | 1Mbps | 10Mbps+ |

#### 软件环境

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Nginx（可选）
sudo apt install nginx -y
```

### 生产配置

#### 1. 环境变量配置

创建生产环境配置文件：

```bash
# 创建环境变量文件
cat > .env.production << EOF
# 后端配置
PORT=8080
GIN_MODE=release
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_NAME=shop_order
DB_USER=shop_user
DB_PASSWORD=your_secure_password
JWT_SECRET=your_super_secure_jwt_secret_key
JWT_EXPIRE_HOURS=24
CORS_ORIGINS=https://yourdomain.com

# 前端配置
VITE_API_BASE_URL=https://yourdomain.com/api
EOF
```

#### 2. Docker Compose 生产配置

创建生产环境编排文件：

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
      - DB_TYPE=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=shop_order
      - DB_USER=shop_user
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGINS=${CORS_ORIGINS}
    depends_on:
      - mysql
    restart: unless-stopped
    volumes:
      - backend_uploads:/app/uploads

  frontend:
    build: ./shop-order-system
    ports:
      - "3000:80"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
    depends_on:
      - backend
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=shop_order
      - MYSQL_USER=shop_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backup:/backup
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  backend_uploads:
  mysql_data:
```

#### 3. Nginx 反向代理配置

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8080;
    }

    upstream frontend {
        server frontend:80;
    }

    server {
        listen 80;
        server_name yourdomain.com;

        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # API代理
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 前端代理
        location / {
            proxy_pass http://frontend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### 部署步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd gglist

# 2. 配置环境变量
cp .env.production .env

# 3. 启动生产服务
docker-compose -f docker-compose.prod.yml up -d

# 4. 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 5. 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## ☁️ 云端部署

### AWS 部署

#### 使用 EC2 + RDS

```bash
# 1. 创建EC2实例
# 2. 安装Docker和Docker Compose
# 3. 配置RDS MySQL数据库
# 4. 更新环境变量
# 5. 部署应用
```

#### 使用 ECS

```bash
# 1. 创建ECS集群
# 2. 构建并推送Docker镜像到ECR
# 3. 创建任务定义
# 4. 创建服务
# 5. 配置负载均衡器
```

### 阿里云部署

#### 使用 ECS + RDS

```bash
# 1. 创建ECS实例
# 2. 配置安全组
# 3. 安装运行环境
# 4. 配置RDS数据库
# 5. 部署应用
```

## 🔒 安全配置

### SSL/TLS 配置

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 数据库安全

```bash
# MySQL安全配置
mysql_secure_installation
```

## 📊 监控和维护

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/docker-containers
```

### 备份策略

```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec mysql mysqldump -u root -p shop_order > backup/db_backup_$DATE.sql
```

### 性能监控

```bash
# 安装监控工具
docker run -d --name=grafana -p 3001:3000 grafana/grafana
docker run -d --name=prometheus -p 9090:9090 prom/prometheus
```

## 🚨 故障排除

### 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 端口冲突 | 端口被占用 | 修改docker-compose.yml中的端口映射 |
| 数据库连接失败 | 配置错误 | 检查数据库配置和网络连接 |
| 前端无法访问API | CORS配置 | 检查CORS_ORIGINS配置 |
| SSL证书错误 | 证书过期 | 更新SSL证书 |

### 调试命令

```bash
# 检查容器状态
docker-compose ps

# 查看容器日志
docker-compose logs [service-name]

# 进入容器调试
docker-compose exec [service-name] sh

# 检查网络连接
docker-compose exec backend ping mysql
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. 查看 [故障排除文档](../development/TROUBLESHOOTING.md)
2. 搜索 [GitHub Issues](../../issues)
3. 提交新的 [Issue](../../issues/new)
4. 联系技术支持团队

---

**🎯 部署成功后，请及时修改默认密码和配置，确保系统安全！**
