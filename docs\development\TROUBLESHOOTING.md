# 故障排除指南

本文档收集了小商店订单管理系统在开发和部署过程中可能遇到的常见问题及其解决方案。

## 🔧 开发环境问题

### Node.js 相关问题

#### 问题：npm install 失败
```bash
错误信息：npm ERR! network timeout
```

**解决方案：**
```bash
# 1. 切换npm镜像源
npm config set registry https://registry.npmmirror.com

# 2. 清理npm缓存
npm cache clean --force

# 3. 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 问题：Node.js版本不兼容
```bash
错误信息：engine "node" is incompatible with this module
```

**解决方案：**
```bash
# 1. 检查Node.js版本
node --version

# 2. 升级到Node.js 16+
# Windows: 下载安装包
# Mac: brew install node
# Linux: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
```

### Go 相关问题

#### 问题：Go模块下载失败
```bash
错误信息：go: module lookup disabled by GOPROXY=off
```

**解决方案：**
```bash
# 1. 设置Go代理
go env -w GOPROXY=https://goproxy.cn,direct

# 2. 清理模块缓存
go clean -modcache

# 3. 重新下载依赖
go mod tidy
```

#### 问题：Go版本过低
```bash
错误信息：go: go.mod requires go >= 1.19
```

**解决方案：**
```bash
# 1. 检查Go版本
go version

# 2. 升级Go版本
# 下载最新版本：https://golang.org/dl/
```

## 🐳 Docker 相关问题

### 容器启动问题

#### 问题：端口冲突
```bash
错误信息：bind: address already in use
```

**解决方案：**
```bash
# 1. 查看端口占用
netstat -tulpn | grep :8080
# 或
lsof -i :8080

# 2. 停止占用端口的进程
sudo kill -9 <PID>

# 3. 或修改docker-compose.yml中的端口映射
ports:
  - "8081:8080"  # 改为其他端口
```

#### 问题：容器构建失败
```bash
错误信息：failed to solve with frontend dockerfile.v0
```

**解决方案：**
```bash
# 1. 清理Docker缓存
docker system prune -a

# 2. 重新构建镜像
docker-compose build --no-cache

# 3. 检查Dockerfile语法
docker build -t test-image .
```

### 数据库连接问题

#### 问题：MySQL连接失败
```bash
错误信息：dial tcp connect: connection refused
```

**解决方案：**
```bash
# 1. 检查MySQL容器状态
docker-compose ps mysql

# 2. 查看MySQL日志
docker-compose logs mysql

# 3. 等待MySQL完全启动
docker-compose up mysql
# 等待看到 "ready for connections" 消息

# 4. 检查网络连接
docker-compose exec backend ping mysql
```

## 🌐 网络和API问题

### CORS 错误

#### 问题：跨域请求被阻止
```bash
错误信息：Access to fetch at 'http://localhost:8080/api' from origin 'http://localhost:5173' has been blocked by CORS policy
```

**解决方案：**
```bash
# 1. 检查后端CORS配置
# 在backend/main.go中确认CORS设置

# 2. 更新CORS_ORIGINS环境变量
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# 3. 重启后端服务
```

### API 调用失败

#### 问题：API返回404
```bash
错误信息：404 Not Found
```

**解决方案：**
```bash
# 1. 检查API路径是否正确
# 前端：VITE_API_BASE_URL=http://localhost:8080/api
# 后端：路由是否正确注册

# 2. 检查后端服务状态
curl http://localhost:8080/health

# 3. 查看后端日志
docker-compose logs backend
```

#### 问题：JWT认证失败
```bash
错误信息：401 Unauthorized
```

**解决方案：**
```bash
# 1. 检查JWT_SECRET配置
# 确保前后端使用相同的密钥

# 2. 检查token是否过期
# 重新登录获取新token

# 3. 检查Authorization头格式
Authorization: Bearer <token>
```

## 💾 数据库问题

### SQLite 问题

#### 问题：数据库文件权限错误
```bash
错误信息：unable to open database file
```

**解决方案：**
```bash
# 1. 检查文件权限
ls -la backend/data/

# 2. 修改权限
chmod 666 backend/data/shop_order.db
chmod 755 backend/data/

# 3. 检查目录是否存在
mkdir -p backend/data
```

### MySQL 问题

#### 问题：字符集问题
```bash
错误信息：Incorrect string value
```

**解决方案：**
```sql
-- 1. 检查字符集
SHOW VARIABLES LIKE 'character_set%';

-- 2. 修改数据库字符集
ALTER DATABASE shop_order CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 修改表字符集
ALTER TABLE customers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 🎨 前端问题

### Vue 相关问题

#### 问题：组件渲染错误
```bash
错误信息：Maximum recursive updates exceeded
```

**解决方案：**
```javascript
// 1. 检查watch监听器是否有循环依赖
// 2. 使用nextTick避免循环更新
import { nextTick } from 'vue'

watch(data, async (newVal) => {
  await nextTick()
  // 处理逻辑
})

// 3. 使用标志位防止循环
const isUpdating = ref(false)
```

#### 问题：路由跳转失败
```bash
错误信息：NavigationDuplicated
```

**解决方案：**
```javascript
// 1. 检查路由配置
// 2. 使用try-catch处理路由跳转
try {
  await router.push('/path')
} catch (error) {
  if (error.name !== 'NavigationDuplicated') {
    throw error
  }
}
```

### Element Plus 问题

#### 问题：组件样式异常
```bash
错误信息：组件显示不正常
```

**解决方案：**
```javascript
// 1. 检查Element Plus导入
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 2. 检查主题配置
// 3. 清理浏览器缓存
```

## 🔐 认证和权限问题

### 登录问题

#### 问题：登录后立即退出
```bash
错误信息：Token验证失败
```

**解决方案：**
```javascript
// 1. 检查token存储
localStorage.getItem('token')

// 2. 检查token格式
// 3. 检查JWT_SECRET配置
// 4. 清理localStorage重新登录
localStorage.clear()
```

## 📱 移动端问题

### 响应式问题

#### 问题：移动端显示异常
```bash
错误信息：页面在移动设备上显示不正常
```

**解决方案：**
```css
/* 1. 检查viewport设置 */
<meta name="viewport" content="width=device-width, initial-scale=1.0">

/* 2. 检查CSS媒体查询 */
@media (max-width: 768px) {
  /* 移动端样式 */
}

/* 3. 使用Element Plus的响应式工具 */
```

## 🚀 性能问题

### 页面加载慢

#### 问题：首屏加载时间过长
```bash
错误信息：页面加载超过5秒
```

**解决方案：**
```javascript
// 1. 启用代码分割
const AsyncComponent = defineAsyncComponent(() => import('./Component.vue'))

// 2. 优化图片资源
// 3. 启用Gzip压缩
// 4. 使用CDN加速
```

### 内存泄漏

#### 问题：页面使用时间长后变慢
```bash
错误信息：内存使用持续增长
```

**解决方案：**
```javascript
// 1. 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handler)
})

// 2. 清理定时器
onUnmounted(() => {
  clearInterval(timer)
})

// 3. 使用弱引用
const weakMap = new WeakMap()
```

## 🛠️ 调试工具

### 浏览器调试

```javascript
// 1. 开启Vue DevTools
// 2. 使用console.log调试
console.log('Debug info:', data)

// 3. 使用断点调试
debugger;

// 4. 网络面板查看API请求
```

### 后端调试

```go
// 1. 使用fmt.Println调试
fmt.Println("Debug info:", data)

// 2. 使用日志库
log.Printf("Error: %v", err)

// 3. 使用Delve调试器
go install github.com/go-delve/delve/cmd/dlv@latest
dlv debug main.go
```

## 📞 获取帮助

### 自助排查步骤

1. **查看错误日志** - 仔细阅读错误信息
2. **搜索文档** - 在本文档中搜索关键词
3. **检查配置** - 确认环境变量和配置文件
4. **重启服务** - 尝试重启相关服务
5. **清理缓存** - 清理各种缓存文件

### 寻求帮助

1. **GitHub Issues** - [提交问题](../../issues)
2. **讨论区** - [参与讨论](../../discussions)
3. **邮件支持** - 联系开发团队
4. **社区论坛** - 在相关技术论坛提问

### 提交Bug报告

请包含以下信息：

- **环境信息** - 操作系统、浏览器、版本等
- **重现步骤** - 详细的操作步骤
- **错误信息** - 完整的错误日志
- **期望结果** - 期望的正常行为
- **实际结果** - 实际发生的情况

---

**💡 提示：大多数问题都可以通过重启服务、清理缓存或检查配置来解决。**
