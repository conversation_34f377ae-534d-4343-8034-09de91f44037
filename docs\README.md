# 小商店订单管理系统 - 文档中心

欢迎来到小商店订单管理系统的文档中心！这里包含了系统的完整文档，帮助您快速上手和深入了解系统。

## 📚 文档导航

### 🎯 用户指南
> 面向最终用户的操作指南

- [快速开始指南](user-guide/快速开始指南.md) - 系统快速入门
- [功能使用手册](user-guide/) - 详细功能说明
- [常见问题解答](user-guide/) - FAQ和故障排除

### 🛠️ 开发文档
> 面向开发者的技术文档

- [开发指导文档](development/开发指导文档.md) - 开发环境搭建和开发指南
- [开发计划](development/开发计划.md) - 项目开发计划和里程碑
- [项目开发总结](development/项目开发完成总结_2025-06-17.md) - 开发过程总结
- [测试文档](development/tests/) - 测试用例和测试指南
- [代码规范](development/) - 编码标准和最佳实践

### 🚀 部署文档
> 面向运维人员的部署指南

- [本地部署指南](deployment/README-本地启动.md) - 本地环境部署
- [生产环境部署](deployment/) - 生产环境配置和部署
- [Docker部署](deployment/) - 容器化部署指南
- [运维手册](deployment/) - 系统监控和维护

### 🏗️ 架构文档
> 系统架构和设计文档

- [数据库设计](architecture/database/) - 数据库表结构和关系
- [系统架构](architecture/) - 整体架构设计
- [API设计](architecture/) - 接口设计规范
- [技术选型](architecture/) - 技术栈说明

### 📖 API文档
> 接口文档和调用说明

- [API概览](api/) - 接口总览
- [认证接口](api/) - 用户认证相关接口
- [业务接口](api/) - 核心业务接口
- [工具接口](api/) - 辅助功能接口

## 🔍 快速查找

### 按角色查找文档

| 角色 | 推荐文档 |
|------|----------|
| 🏪 **商店老板** | [快速开始指南](user-guide/快速开始指南.md) |
| 👨‍💻 **开发者** | [开发指导文档](development/开发指导文档.md) |
| 🔧 **运维人员** | [部署文档](deployment/) |
| 🎨 **设计师** | [架构文档](architecture/) |

### 按功能查找文档

| 功能模块 | 相关文档 |
|----------|----------|
| 🛍️ **商品管理** | [用户指南](user-guide/) \| [API文档](api/) |
| 👥 **客户管理** | [用户指南](user-guide/) \| [API文档](api/) |
| 📋 **订单管理** | [用户指南](user-guide/) \| [API文档](api/) |
| 📅 **生产日历** | [用户指南](user-guide/) \| [开发文档](development/) |
| 👤 **用户管理** | [用户指南](user-guide/) \| [API文档](api/) |

## 📝 文档贡献

我们欢迎您为文档做出贡献！

### 如何贡献文档

1. **发现问题** - 在使用过程中发现文档问题
2. **提交Issue** - 在GitHub上提交文档问题
3. **修改文档** - Fork项目并修改文档
4. **提交PR** - 提交Pull Request

### 文档规范

- 使用Markdown格式编写
- 遵循统一的文档结构
- 包含必要的代码示例
- 添加适当的图片和图表

## 🆘 获取帮助

如果您在使用文档过程中遇到问题：

1. 📖 **查看FAQ** - [常见问题解答](user-guide/)
2. 🔍 **搜索文档** - 使用Ctrl+F搜索关键词
3. 💬 **提交Issue** - [GitHub Issues](../../issues)
4. 📧 **联系我们** - 发送邮件给开发团队

## 📅 文档更新日志

| 日期 | 版本 | 更新内容 |
|------|------|----------|
| 2025-06-17 | v1.0.0 | 初始文档结构创建 |
| 2025-06-17 | v1.0.1 | 完善用户指南和开发文档 |

---

**💡 提示：建议将此页面加入书签，方便快速访问各类文档！**
