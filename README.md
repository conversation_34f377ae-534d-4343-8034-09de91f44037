# 小商店订单管理系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Go Version](https://img.shields.io/badge/Go-1.19+-blue.svg)](https://golang.org)
[![Vue Version](https://img.shields.io/badge/Vue-3.0+-green.svg)](https://vuejs.org)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

一个基于 Vue 3 + Go 的现代化订单管理系统，专为小型商店设计。提供完整的商品管理、客户管理、订单处理和生产计划功能。

## ✨ 功能特性

- 🛍️ **商品管理** - 商品信息维护、库存管理、分类管理、价格管理
- 👥 **客户管理** - 客户档案、地址管理、联系方式、购买历史
- 📋 **订单管理** - 订单创建、状态跟踪、支付管理、订单统计
- 📅 **生产日历** - 订单排程、生产计划、日程安排、工作负载
- 👤 **用户管理** - 角色权限、用户认证、操作日志、权限控制
- ⚙️ **定时任务** - 自动化任务、数据同步、系统维护、报表生成

## 🚀 技术栈

### 前端技术
- **Vue 3** - 渐进式 JavaScript 框架
- **Element Plus** - Vue 3 企业级组件库
- **Pinia** - Vue 状态管理库
- **Vue Router** - 官方路由管理器
- **Vite** - 下一代前端构建工具

### 后端技术
- **Go 1.19+** - 高性能后端语言
- **Gin** - 高性能 Web 框架
- **XORM** - 简单而强大的 ORM
- **SQLite** - 轻量级嵌入式数据库
- **JWT** - JSON Web Token 身份认证

### 开发工具
- **Docker** - 容器化部署
- **Git** - 版本控制
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化

## 📦 快速开始

### 环境要求

- **Node.js** 16.0+
- **Go** 1.19+
- **Git** 2.0+

### 🔧 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd gglist
```

2. **启动后端服务**
```bash
cd backend
go mod tidy
go run main.go
```
后端服务将在 `http://localhost:8080` 启动

3. **启动前端服务**
```bash
cd shop-order-system
npm install
npm run dev
```
前端服务将在 `http://localhost:5173` 启动

4. **访问应用**
- 前端地址：http://localhost:5173
- 后端API：http://localhost:8080/api

### 🔑 默认账户

| 角色 | 邮箱 | 密码 | 权限 |
|------|------|------|------|
| 超级管理员 | <EMAIL> | demo123 | 全部功能 |
| 普通员工 | <EMAIL> | demo123 | 业务功能 |

## 📁 项目结构

```
gglist/
├── backend/                    # Go 后端服务
│   ├── internal/              # 内部包
│   │   ├── controllers/       # 控制器层
│   │   ├── models/           # 数据模型
│   │   ├── middleware/       # 中间件
│   │   └── utils/            # 工具函数
│   ├── data/                 # 数据库文件
│   ├── uploads/              # 文件上传目录
│   └── main.go               # 服务入口
├── shop-order-system/         # Vue 前端应用
│   ├── src/                  # 源代码
│   │   ├── components/       # 组件
│   │   ├── views/           # 页面
│   │   ├── stores/          # 状态管理
│   │   ├── router/          # 路由配置
│   │   └── lib/             # 工具库
│   ├── public/              # 静态资源
│   └── package.json         # 依赖配置
├── docs/                     # 项目文档
│   ├── api/                 # API 文档
│   ├── architecture/        # 架构文档
│   ├── deployment/          # 部署文档
│   ├── development/         # 开发文档
│   └── user-guide/          # 用户指南
├── docker-compose.yml        # Docker 编排
└── README.md                # 项目说明
```

## 📚 文档

| 文档类型 | 描述 | 链接 |
|----------|------|------|
| 🎯 用户指南 | 系统使用说明和操作手册 | [docs/user-guide/](docs/user-guide/) |
| 🛠️ 开发文档 | 开发指南、API文档、代码规范 | [docs/development/](docs/development/) |
| 🚀 部署文档 | 部署指南、运维手册、配置说明 | [docs/deployment/](docs/deployment/) |
| 🏗️ 架构文档 | 系统架构、数据库设计、技术选型 | [docs/architecture/](docs/architecture/) |

## 🔨 开发

### 开发环境设置

详细的开发环境配置请参考：[开发指导文档](docs/development/开发指导文档.md)

### API 接口

完整的 API 文档请查看：[API 文档](docs/api/)

### 代码规范

- 前端遵循 Vue 3 官方风格指南
- 后端遵循 Go 官方代码规范
- 使用 ESLint 和 gofmt 进行代码格式化

## 🐳 部署

### Docker 快速部署

```bash
# 使用 Docker Compose 一键部署
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境部署

详细的生产环境部署指南请参考：[部署文档](docs/deployment/)

## 🤝 贡献

我们欢迎所有形式的贡献！

1. **Fork** 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 **Pull Request**

### 贡献指南

- 提交前请确保代码通过所有测试
- 遵循现有的代码风格和规范
- 为新功能添加相应的测试用例
- 更新相关文档

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 详见 LICENSE 文件了解更多信息。

## 🆘 支持与反馈

- 🐛 **Bug 报告**：[提交 Issue](../../issues)
- 💡 **功能建议**：[功能请求](../../issues)
- 📧 **技术支持**：联系开发团队
- 📖 **文档问题**：[文档反馈](../../issues)

## 🏆 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**
