# API 文档

小商店订单管理系统 RESTful API 文档

## 📋 API 概览

### 基础信息

- **Base URL**: `http://localhost:8080/api`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

### HTTP 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔐 认证接口

### 用户登录
```http
POST /api/auth/login
```

**请求参数**
```json
{
  "email": "<EMAIL>",
  "password": "demo123"
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "super_admin",
      "is_active": true
    }
  }
}
```

### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer {token}
```

## 👥 客户管理接口

### 获取客户列表
```http
GET /api/customers
```

**查询参数**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `sort`: 排序字段

### 创建客户
```http
POST /api/customers
```

**请求参数**
```json
{
  "name": "客户姓名",
  "contact": "联系方式"
}
```

### 获取客户详情
```http
GET /api/customers/{id}
```

### 更新客户
```http
PUT /api/customers/{id}
```

### 删除客户
```http
DELETE /api/customers/{id}
```

## 📍 地址管理接口

### 获取客户地址列表
```http
GET /api/customers/{customer_id}/addresses
```

### 创建地址
```http
POST /api/customers/{customer_id}/addresses
```

**请求参数**
```json
{
  "recipient_name": "收件人姓名",
  "recipient_phone": "收件人电话",
  "address_details": "详细地址",
  "is_default": false
}
```

### 更新地址
```http
PUT /api/addresses/{id}
```

### 删除地址
```http
DELETE /api/addresses/{id}
```

### 设置默认地址
```http
PUT /api/addresses/{id}/default
```

## 🛍️ 商品管理接口

### 获取商品列表
```http
GET /api/products
```

**查询参数**
- `page`: 页码
- `limit`: 每页数量
- `category`: 分类筛选
- `is_listed`: 上架状态
- `search`: 搜索关键词

### 创建商品
```http
POST /api/products
```

**请求参数**
```json
{
  "name": "商品名称",
  "category": "商品分类",
  "price": 99.99,
  "is_listed": true,
  "custom_attributes": {},
  "stock_quantity": 100,
  "min_stock_level": 10,
  "max_stock_level": 1000,
  "stock_unit": "件"
}
```

### 获取商品详情
```http
GET /api/products/{id}
```

### 更新商品
```http
PUT /api/products/{id}
```

### 删除商品
```http
DELETE /api/products/{id}
```

## 📋 订单管理接口

### 获取订单列表
```http
GET /api/orders
```

**查询参数**
- `page`: 页码
- `limit`: 每页数量
- `status`: 订单状态
- `payment_status`: 付款状态
- `start_date`: 开始日期
- `end_date`: 结束日期

### 创建订单
```http
POST /api/orders
```

**请求参数**
```json
{
  "customer_id": 1,
  "delivery_datetime": "2025-06-18T10:00:00Z",
  "delivery_address": "送货地址",
  "payment_status": "unpaid",
  "order_status": "pending",
  "notes": "备注信息",
  "total_price": 99.99,
  "order_items": [
    {
      "product_id": 1,
      "quantity": 2,
      "price_at_order": 49.99,
      "selected_attributes": {}
    }
  ]
}
```

### 获取订单详情
```http
GET /api/orders/{id}
```

### 更新订单
```http
PUT /api/orders/{id}
```

### 删除订单
```http
DELETE /api/orders/{id}
```

### 更新订单状态
```http
PUT /api/orders/{id}/status
```

**请求参数**
```json
{
  "order_status": "production"
}
```

### 更新付款状态
```http
PUT /api/orders/{id}/payment
```

**请求参数**
```json
{
  "payment_status": "paid"
}
```

## 📊 统计接口

### 获取仪表盘数据
```http
GET /api/dashboard/stats
```

### 获取销售统计
```http
GET /api/stats/sales
```

**查询参数**
- `period`: 统计周期 (day/week/month/year)
- `start_date`: 开始日期
- `end_date`: 结束日期

## 🔧 工具接口

### 健康检查
```http
GET /api/health
```

### 系统信息
```http
GET /api/system/info
```

## 📝 错误代码

| 错误代码 | 说明 |
|----------|------|
| INVALID_PARAMS | 请求参数无效 |
| UNAUTHORIZED | 未授权访问 |
| FORBIDDEN | 禁止访问 |
| NOT_FOUND | 资源不存在 |
| DUPLICATE_ENTRY | 数据重复 |
| INTERNAL_ERROR | 服务器内部错误 |

## 🧪 测试工具

推荐使用以下工具测试API：

- **Postman** - GUI测试工具
- **curl** - 命令行工具
- **HTTPie** - 现代命令行工具

### 示例请求

```bash
# 登录获取token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"demo123"}'

# 使用token访问API
curl -X GET http://localhost:8080/api/customers \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

**📝 注意：此文档会随着API的更新而持续维护，请关注最新版本。**
