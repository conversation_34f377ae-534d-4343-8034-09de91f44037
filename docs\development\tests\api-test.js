// API功能测试脚本
// 用于验证主要API功能是否正常工作

const API_BASE = 'http://localhost:8080/api'

// 测试用的认证token（需要先登录获取）
let authToken = ''

// 测试结果统计
const testResults = {
  passed: 0,
  failed: 0,
  total: 0
}

// 辅助函数：发送API请求
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  }
  
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers
    })
    
    const data = await response.json()
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 测试函数
async function runTest(testName, testFn) {
  testResults.total++
  console.log(`\n🧪 测试: ${testName}`)
  
  try {
    const result = await testFn()
    if (result) {
      console.log(`✅ 通过: ${testName}`)
      testResults.passed++
    } else {
      console.log(`❌ 失败: ${testName}`)
      testResults.failed++
    }
  } catch (error) {
    console.log(`❌ 失败: ${testName} - ${error.message}`)
    testResults.failed++
  }
}

// 1. 测试用户认证
async function testAuth() {
  // 测试登录
  const loginResult = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    })
  })

  if (loginResult.success && loginResult.data.token) {
    authToken = loginResult.data.token
    console.log('  ✓ 登录成功')
    
    // 测试获取用户信息
    const profileResult = await apiRequest('/user/profile')
    if (profileResult.success) {
      console.log('  ✓ 获取用户信息成功')
      return true
    }
  }
  
  console.log('  ✗ 认证测试失败')
  return false
}

// 2. 测试客户管理
async function testCustomers() {
  // 获取客户列表
  const listResult = await apiRequest('/customers')
  if (!listResult.success) {
    console.log('  ✗ 获取客户列表失败')
    return false
  }
  console.log(`  ✓ 获取客户列表成功 (${listResult.data.length} 条记录)`)

  // 创建测试客户
  const createResult = await apiRequest('/customers', {
    method: 'POST',
    body: JSON.stringify({
      name: '测试客户',
      contact: '13800138000'
    })
  })
  
  if (!createResult.success) {
    console.log('  ✗ 创建客户失败')
    return false
  }
  console.log('  ✓ 创建客户成功')
  
  const customerId = createResult.data.id
  
  // 更新客户
  const updateResult = await apiRequest(`/customers/${customerId}`, {
    method: 'PUT',
    body: JSON.stringify({
      name: '测试客户（已更新）',
      contact: '13800138001'
    })
  })
  
  if (!updateResult.success) {
    console.log('  ✗ 更新客户失败')
    return false
  }
  console.log('  ✓ 更新客户成功')
  
  // 删除客户
  const deleteResult = await apiRequest(`/customers/${customerId}`, {
    method: 'DELETE'
  })
  
  if (!deleteResult.success) {
    console.log('  ✗ 删除客户失败')
    return false
  }
  console.log('  ✓ 删除客户成功')
  
  return true
}

// 3. 测试商品管理
async function testProducts() {
  // 获取商品列表
  const listResult = await apiRequest('/products')
  if (!listResult.success) {
    console.log('  ✗ 获取商品列表失败')
    return false
  }
  console.log(`  ✓ 获取商品列表成功 (${listResult.data.length} 条记录)`)

  // 创建测试商品
  const createResult = await apiRequest('/products', {
    method: 'POST',
    body: JSON.stringify({
      name: '测试商品',
      category: '测试分类',
      price: 99.99,
      is_listed: true,
      stock_quantity: 100,
      min_stock_level: 10,
      max_stock_level: 1000,
      stock_unit: '件'
    })
  })
  
  if (!createResult.success) {
    console.log('  ✗ 创建商品失败')
    return false
  }
  console.log('  ✓ 创建商品成功')
  
  const productId = createResult.data.id
  
  // 更新库存
  const stockResult = await apiRequest(`/products/${productId}/stock`, {
    method: 'PUT',
    body: JSON.stringify({
      type: 'in',
      quantity: 50,
      reason: '测试入库'
    })
  })
  
  if (!stockResult.success) {
    console.log('  ✗ 更新库存失败')
    return false
  }
  console.log('  ✓ 更新库存成功')
  
  // 获取低库存商品
  const lowStockResult = await apiRequest('/products/low-stock')
  if (!lowStockResult.success) {
    console.log('  ✗ 获取低库存商品失败')
    return false
  }
  console.log('  ✓ 获取低库存商品成功')
  
  // 删除商品
  const deleteResult = await apiRequest(`/products/${productId}`, {
    method: 'DELETE'
  })
  
  if (!deleteResult.success) {
    console.log('  ✗ 删除商品失败')
    return false
  }
  console.log('  ✓ 删除商品成功')
  
  return true
}

// 4. 测试定时任务管理
async function testScheduler() {
  // 获取定时任务状态
  const statusResult = await apiRequest('/scheduler/status')
  if (!statusResult.success) {
    console.log('  ✗ 获取定时任务状态失败')
    return false
  }
  console.log('  ✓ 获取定时任务状态成功')

  // 获取任务列表
  const jobsResult = await apiRequest('/scheduler/jobs')
  if (!jobsResult.success) {
    console.log('  ✗ 获取任务列表失败')
    return false
  }
  console.log(`  ✓ 获取任务列表成功 (${jobsResult.data.length} 个任务)`)
  
  return true
}

// 5. 测试节假日管理
async function testHolidays() {
  // 获取节假日列表
  const listResult = await apiRequest('/holidays?year=2025')
  if (!listResult.success) {
    console.log('  ✗ 获取节假日列表失败')
    return false
  }
  console.log(`  ✓ 获取节假日列表成功 (${listResult.data.length} 条记录)`)

  // 测试农历转换
  const lunarResult = await apiRequest('/calendar/lunar?date=2025-06-17')
  if (!lunarResult.success) {
    console.log('  ✗ 农历转换失败')
    return false
  }
  console.log('  ✓ 农历转换成功')
  
  return true
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始API功能测试...\n')
  
  await runTest('用户认证', testAuth)
  await runTest('客户管理', testCustomers)
  await runTest('商品管理', testProducts)
  await runTest('定时任务管理', testScheduler)
  await runTest('节假日管理', testHolidays)
  
  console.log('\n📊 测试结果统计:')
  console.log(`总计: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`)
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试通过！')
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关功能')
  }
}

// 在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests }
} else {
  // 在浏览器中运行
  runAllTests()
}
