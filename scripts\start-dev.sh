#!/bin/bash

echo "========================================"
echo "小商店订单管理系统 - 开发环境启动脚本"
echo "========================================"
echo

echo "[1/3] 检查环境..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js 16+"
    exit 1
fi

# 检查 Go
if ! command -v go &> /dev/null; then
    echo "错误: 未找到 Go，请先安装 Go 1.19+"
    exit 1
fi

echo "[2/3] 启动后端服务..."
cd backend
gnome-terminal --title="后端服务" -- bash -c "go run main.go; exec bash" &
cd ..

echo "[3/3] 启动前端服务..."
cd shop-order-system
gnome-terminal --title="前端服务" -- bash -c "npm run dev; exec bash" &
cd ..

echo
echo "========================================"
echo "启动完成！"
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:8080"
echo "========================================"
echo
echo "按 Ctrl+C 退出"

# 等待用户中断
trap 'echo "正在关闭服务..."; exit 0' INT
while true; do
    sleep 1
done
