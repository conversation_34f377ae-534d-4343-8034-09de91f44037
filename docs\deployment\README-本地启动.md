# 小商店订单管理系统 - 本地启动指南

本文档介绍如何在本地环境中启动系统，无需使用Docker。

## 系统要求

### 必需软件
- **Go 1.19+** - 后端服务
- **Node.js 16+** - 前端服务
- **npm** - 包管理器（通常随Node.js安装）

### 可选软件
- **Git** - 版本控制
- **curl** - 用于健康检查（Windows可能需要单独安装）

## 快速开始

### Windows用户

1. **使用本地启动脚本**
   ```cmd
   # 启动完整服务（后端 + 前端）
   start-local.bat start
   
   # 仅启动后端服务
   start-local.bat backend
   
   # 仅启动前端服务
   start-local.bat frontend
   
   # 安装依赖
   start-local.bat install
   
   # 查看帮助
   start-local.bat help
   ```

2. **手动启动方式**
   ```cmd
   # 启动后端服务
   cd backend
   go run main.go
   
   # 新开命令行窗口，启动前端服务
   cd shop-order-system
   npm run dev
   ```

### Linux/macOS用户

1. **使用本地启动脚本**
   ```bash
   # 给脚本添加执行权限（仅首次需要）
   chmod +x start-local.sh
   
   # 启动完整服务（后端 + 前端）
   ./start-local.sh start
   
   # 仅启动后端服务
   ./start-local.sh backend
   
   # 仅启动前端服务
   ./start-local.sh frontend
   
   # 安装依赖
   ./start-local.sh install
   
   # 停止服务
   ./start-local.sh stop
   
   # 查看服务状态
   ./start-local.sh status
   
   # 查看日志
   ./start-local.sh logs
   
   # 查看帮助
   ./start-local.sh help
   ```

2. **手动启动方式**
   ```bash
   # 启动后端服务
   cd backend
   go run main.go &
   
   # 启动前端服务
   cd shop-order-system
   npm run dev &
   ```

## 服务地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:5173 或 http://localhost:5174
- **后端API**: http://localhost:8082
- **健康检查**: http://localhost:8082/health

## 默认账户

系统提供以下默认账户用于测试：

- **管理员**: <EMAIL> / demo123
- **员工**: <EMAIL> / demo123

## 环境配置

### 后端配置 (backend/.env)
```env
PORT=8082
DB_TYPE=sqlite
DB_PATH=./data/shop_order.db
JWT_SECRET=your-secret-key-here
```

### 前端配置 (shop-order-system/.env)
```env
VITE_API_BASE_URL=http://localhost:8082/api
VITE_APP_TITLE=小商店订单管理系统
```

## 常见问题

### 1. 端口冲突
如果8082端口被占用，可以修改`backend/.env`中的`PORT`配置，同时更新前端`.env`中的`VITE_API_BASE_URL`。

### 2. 依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf shop-order-system/node_modules
cd shop-order-system
npm install

# Go模块问题
cd backend
go clean -modcache
go mod tidy
```

### 3. 数据库问题
SQLite数据库文件位于`backend/data/shop_order.db`，如果遇到数据库问题，可以删除此文件让系统重新创建。

### 4. 权限问题（Linux/macOS）
```bash
# 确保脚本有执行权限
chmod +x start-local.sh

# 确保目录有写权限
chmod -R 755 backend/data
chmod -R 755 backend/uploads
```

## 开发模式

### 热重载
- **前端**: Vite自动提供热重载功能
- **后端**: 可以使用`air`工具实现热重载
  ```bash
  # 安装air
  go install github.com/cosmtrek/air@latest
  
  # 在backend目录下运行
  air
  ```

### 调试模式
- **前端**: 浏览器开发者工具
- **后端**: 使用Go调试器或IDE调试功能

## 生产部署

本地启动脚本仅用于开发环境。生产环境建议：

1. 使用Docker部署（参考`start.bat`）
2. 使用进程管理器（如PM2、systemd）
3. 配置反向代理（如Nginx）
4. 使用HTTPS
5. 配置环境变量和安全设置

## 故障排除

### 查看日志
- **Windows**: 查看命令行窗口输出
- **Linux/macOS**: 查看`backend.log`和`frontend.log`文件

### 重启服务
```bash
# Linux/macOS
./start-local.sh stop
./start-local.sh start

# Windows
# 关闭命令行窗口，重新运行start-local.bat
```

### 完全重置
```bash
# 停止所有服务
# 删除数据库文件
rm -f backend/data/shop_order.db

# 重新安装依赖
./start-local.sh install

# 重新启动
./start-local.sh start
```

## 支持

如果遇到问题，请检查：
1. 系统要求是否满足
2. 端口是否被占用
3. 防火墙设置
4. 日志文件中的错误信息

更多信息请参考项目文档或联系开发团队。
