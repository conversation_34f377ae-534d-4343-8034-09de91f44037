# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 依赖目录
node_modules/
jspm_packages/

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的编译目录
dist/
build/

# Yarn Integrity文件
.yarn-integrity

# dotenv环境变量文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# Go相关
# 二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 输出的二进制文件
main
main.exe

# Go工作区文件
go.work

# 依赖证明文件
go.sum

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件目录
uploads/
temp/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 临时文件
tmp/
temp/
.tmp/

# 测试覆盖率
coverage/
*.cover
*.coverprofile

# 文档生成
docs/_build/

# PyCharm
.idea/

# 本地配置文件
config.local.json
config.local.yaml

# 证书文件
*.pem
*.key
*.crt

# 本地开发文件
.local/
local/

# 错误日志
error.log
access.log

# 进程文件
*.pid

# 锁文件
*.lock

# 缓存目录
.cache/
cache/

# 构建工具
.grunt
.sass-cache

# 包管理器
.pnpm-debug.log*

# 微信小程序
.miniprogram/

# 移动端
platforms/
plugins/

# 其他
.nyc_output
.coverage
.eslintcache
