#!/bin/bash

echo "========================================"
echo "小商店订单管理系统 - 项目健康检查"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_exists() {
    if [ ! -e "$1" ]; then
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
    return 0
}

check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo -e "${RED}❌ 未找到 $1${NC}"
        return 1
    fi
    return 0
}

# 错误计数
errors=0

echo "[1/8] 检查项目结构..."
check_exists "backend" "后端目录不存在" || ((errors++))
check_exists "shop-order-system" "前端目录不存在" || ((errors++))
check_exists "docs" "文档目录不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 项目结构完整${NC}"
fi

echo "[2/8] 检查环境依赖..."
check_command "node" || ((errors++))
check_command "go" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 环境依赖正常${NC}"
fi

echo "[3/8] 检查后端配置..."
check_exists "backend/go.mod" "后端go.mod文件不存在" || ((errors++))
check_exists "backend/main.go" "后端main.go文件不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 后端配置正常${NC}"
fi

echo "[4/8] 检查前端配置..."
check_exists "shop-order-system/package.json" "前端package.json文件不存在" || ((errors++))
check_exists "shop-order-system/vite.config.js" "前端vite.config.js文件不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 前端配置正常${NC}"
fi

echo "[5/8] 检查Docker配置..."
check_exists "docker-compose.yml" "docker-compose.yml文件不存在" || ((errors++))
check_exists "backend/Dockerfile" "后端Dockerfile不存在" || ((errors++))
check_exists "shop-order-system/Dockerfile" "前端Dockerfile不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ Docker配置正常${NC}"
fi

echo "[6/8] 检查文档完整性..."
check_exists "README.md" "主README文件不存在" || ((errors++))
check_exists "docs/README.md" "文档索引不存在" || ((errors++))
check_exists "docs/api/README.md" "API文档不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 文档完整${NC}"
fi

echo "[7/8] 检查许可证和贡献指南..."
check_exists "LICENSE" "LICENSE文件不存在" || ((errors++))
check_exists "CONTRIBUTING.md" "CONTRIBUTING.md文件不存在" || ((errors++))
check_exists "CHANGELOG.md" "CHANGELOG.md文件不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 许可证和贡献指南完整${NC}"
fi

echo "[8/8] 检查启动脚本..."
check_exists "scripts/start-dev.bat" "Windows启动脚本不存在" || ((errors++))
check_exists "scripts/start-dev.sh" "Linux启动脚本不存在" || ((errors++))
if [ $errors -eq 0 ]; then
    echo -e "${GREEN}✅ 启动脚本完整${NC}"
fi

echo
if [ $errors -eq 0 ]; then
    echo "========================================"
    echo -e "${GREEN}🎉 项目健康检查通过！${NC}"
    echo "========================================"
    echo
    echo -e "项目状态：${GREEN}✅ 生产就绪${NC}"
    echo -e "文档完整性：${GREEN}✅ 100%${NC}"
    echo -e "配置正确性：${GREEN}✅ 正常${NC}"
    echo -e "依赖完整性：${GREEN}✅ 正常${NC}"
    echo
    echo "可以开始使用以下命令："
    echo "- 开发环境：./scripts/start-dev.sh"
    echo "- Docker部署：docker-compose up -d"
    echo "- 查看文档：docs/README.md"
    echo
else
    echo "========================================"
    echo -e "${RED}❌ 项目健康检查失败！${NC}"
    echo "========================================"
    echo
    echo -e "${YELLOW}发现 $errors 个问题，请检查上述错误并修复后重新运行检查。${NC}"
    echo
    exit 1
fi
