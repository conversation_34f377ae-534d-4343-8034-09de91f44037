### **《小商店订单管理系统》- 开发指导文档**

#### **1. 项目概述 (Project Overview)**

* **产品名称：** 小商店订单管理系统 (暂定)
* **产品定位：** 一款为小型实体商店（如烘焙店、花店、定制礼品店等）设计的轻量级订单管理工具。
* **核心目标：** 简化从接单、管理商品/客户、安排生产到交付的全过程，并通过数据可视化提供经营洞察，核心是**实用、高效、易于上手**。
* **使用场景：** 店员在店内通过电脑或平板设备进行操作，快速录入客户订单，并根据日历安排生产和配送计划。

---

#### **2. 用户角色与权限 (User Roles & Permissions)**

根据你的最新想法，我们将用户简化为两种角色，以符合实际操作的便利性。

* **超级管理员 (Super Admin)**

  * **账户来源：** 系统初始化时预设一个账户，首次登录后必须修改密码。
  * **核心权限：**
    * 拥有系统的最高权限。
    * **人员管理：** 创建、编辑、禁用/启用、删除“普通人员”账号，可重置其密码。
    * **完全数据权限：** 对商品、客户、订单等所有数据拥有完整的增、删、改、查（CRUD）权限。
    * **系统配置：** （未来可扩展）如配置店铺信息、节假日同步API等。
    * **访问所有页面：** 包括驾驶舱、日历、所有管理页面。
* **普通人员 (Staff)**

  * **账户来源：** 由“超级管理员”创建。
  * **核心权限：**
    * **商品管理：** 增、删、改、查所有商品信息。
    * **客户管理：** 增、删、改、查所有客户信息。
    * **订单管理：** 增、删、改、查所有订单信息。
    * **访问核心页面：** 可访问并使用驾驶舱、日历、商品/客户/订单管理页面。
  * **权限限制：** 不能管理其他用户账号。

---

#### **3. 功能模块详述 (Detailed Functional Modules)**

##### **3.1 登录与账户体系 (Login & Account System)**

* **登录界面：** 提供用户名/邮箱和密码输入框。
* **首次登录：** 超级管理员和新创建的普通人员首次登录时，系统应强制引导其修改初始密码。
* **账户管理（仅超级管理员）：**
  * 一个列表展示所有“普通人员”账号。
  * 功能包括：创建新账号（设置初始密码）、编辑信息、禁用/启用账号、重置密码。

##### **3.2 驾驶舱/仪表盘 (Dashboard)**

这是系统的首页，用于直观展示核心业务数据。

* **核心数据指标 (可配置时间范围：今日/本周/本月)：**
  * **总销售额：** 所有已付款订单的金额总和。
  * **订单总数：** 总的订单数量。
  * **待交付订单：** 尚未完成交付的订单数量。
  * **新增客户数：** 在选定时间范围内新增的客户数量。
* **可视化图表：**
  * **销售趋势图：** 以折线图展示近期（如近30天）每日的销售额变化。
  * **热门商品排行：** 以条形图展示销量最高的Top 5商品。
  * **订单状态分布：** 以饼图展示“待付款”、“待生产”、“待配送”、“已完成”等状态的订单比例。

##### **3.3 商品管理 (Product Management)**

* **商品列表页：**
  * 以表格或卡片形式展示所有商品，包含缩略图、名称、类别、价格、上架状态。
  * 提供搜索功能（按名称/类别搜索）和筛选功能（按上架状态/类别筛选）。
* **商品编辑/新建页（表单）：**
  * **名称 (Name):** 文本输入，必填。
  * **类别 (Category):** 文本输入或下拉选择（系统可自动记录已有的类别供选择）。
  * **价格 (Price):** 数字输入，必填。
  * **上架状态 (Status):** 开关（Toggle）或下拉选择（上架/下架）。下架商品在录入订单时不可被选择。
  * **图片 (Image):** 图片上传控件，支持单张图片上传和预览。
  * **自定义属性 (Custom Attributes):** 一个灵活的键值对（Key-Value）添加区域。例如：
    * `[规格]: [6寸, 8寸, 10寸]`
    * `[口味]: [巧克力, 香草, 草莓]`
    * `[材质]: [纯棉, 涤纶]`
      在录入订单时，这些属性可以作为选择项。

##### **3.4 客户管理 (Customer Management)**

* **客户列表页：**
  * 以表格形式展示所有客户，包含姓名、联系方式、默认地址。
  * 提供按姓名或联系方式的搜索功能。
* **客户编辑/新建页（表单）：**
  * **姓名 (Name):** 文本输入，必填。
  * **联系方式 (Contact):** 文本输入（可以是手机号、微信号等）。
  * **地址 (Addresses):**
    * 支持动态添加多个地址。
    * 每个地址包含字段：收货人、联系电话、省市区、详细地址。
    * 可以设置一个“默认地址”。

##### **3.5 订单录入与管理 (Order Entry & Management)**

这是系统的核心操作界面，设计上要极致流畅。

* **订单录入界面（一个大表单）：**
  1. **选择客户：**
     * 一个支持搜索的输入框，输入客户姓名/联系方式时，自动弹出匹配的客户列表供选择。
     * 选中客户后，其信息（如默认送货地址）自动填充到下方相应字段。
     * 旁边有一个“**快速新增客户**”按钮，点击后弹出一个简化版的客户录入窗口，录入后该客户自动被选中。
  2. **订单信息：**
     * **送货时间 (Delivery Time):** 日期和时间选择器。
     * **收货信息 (Shipping Info):**
       * **收货人/联系方式：** 默认填充客户信息，可修改。
       * **送货地址：** 如果客户已选，则下拉框中列出该客户的所有地址；也可手动输入新地址。
  3. **商品选择：**
     * 一个“添加商品”按钮，点击后弹出商品选择列表（只显示“上架”商品）。
     * 支持按商品名称/类别搜索。
     * 选择商品后，可填写**数量**，并选择该商品的**自定义属性**（如“规格：8寸”，“口味：巧克力”）。
     * 已选商品会以列表形式展示在订单中，每项都显示商品名、属性、单价、数量、小计。
  4. **费用与状态：**
     * **合计价格 (Total Price):** 系统根据所选商品和数量**自动计算**并显示，不可手动修改。
     * **付款状态 (Payment Status):** 下拉选择或开关（**已付款 / 未付款**），必填。
     * **特殊要求/备注 (Notes):** 一个大的文本输入框，用于记录客户的特殊要求。
* **订单列表页：**
  * 展示所有订单，包含订单号、客户姓名、送货时间、总金额、付款状态、订单状态（如：待生产、待配送、已完成）。
  * 提供强大的搜索和筛选功能（按客户、日期范围、付款状态等）。

##### **3.6 生产/配送日历 (Production/Delivery Calendar)**

* **视图切换：** 支持**年、月、周**三种视图。
* **日期显示：**
  * 每个日期格子内，清晰显示**公历**和**农历**。
  * **法定节假日**自动标记（如显示“休”或特殊背景色）。
* **订单聚合显示：**
  * 在月视图或年视图中，如果当天有订单，则显示一个数字标记，如“**5单**”。
  * 在周视图或日视图（如果未来增加）中，由于空间较大，可以显示更详细的信息，如：“**蛋糕 x 3, 饼干 x 10**”。
  * 点击日期格子可以弹窗或跳转到该日期的订单列表。

---

#### **4. 技术栈与架构建议 (Tech Stack & Architecture)**

* **后端服务 (Backend Service):**

  * **强烈推荐 Supabase。** 理由如下：
    * **数据库 (Database):** 内置强大的 PostgreSQL 数据库，其 `JSONB` 类型完美支持“商品自定义属性”。
    * **认证 (Authentication):** 自带完整的用户认证和管理功能，轻松实现登录、注册和基于角色的访问控制 (Row Level Security)。
    * **存储 (Storage):** 提供文件存储服务，用于存放商品图片等静态资源。
    * **云函数 (Edge Functions):** 可以用来执行服务端逻辑，例如：
      * 创建一个定时任务（Cron Job），每天调用第三方API，自动同步未来一年的法定节假日信息并存入数据库。
      * 处理一些复杂的、需要安全验证的后端计算。
  * **优势：** 极大简化后端开发和运维工作，开发者可以专注于前端业务逻辑。安全、稳定且可扩展。
* **前端框架 (Frontend Framework):**

  * **vue+element**
  * **优势：** 框架和组件库提供了现成的表格、表单、日历、图表等高质量组件，能大幅提升开发效率和界面美观度。

---

#### **5. 数据模型设计 (Data Model Design - for Supabase/Postgres)**

* **`users` (由 Supabase Auth 管理):**

  * `id` (UUID, Primary Key)
  * `email` (Text)
  * `role` (Text, 'super_admin' or 'staff') - 在 `metadata` 字段中定义
* **`products` (商品表):**

  * `id` (Serial, Primary Key)
  * `name` (Text, Not Null)
  * `category` (Text)
  * `price` (Numeric, Not Null)
  * `is_listed` (Boolean, Default: true) - 是否上架
  * `image_url` (Text)
  * `custom_attributes` (JSONB) - 存储 `{ "规格": ["6寸", "8寸"], "口味": ["原味", "巧克力"] }` 这样的结构
* **`customers` (客户表):**

  * `id` (Serial, Primary Key)
  * `name` (Text, Not Null)
  * `contact` (Text) - 存储主要联系方式
* **`addresses` (地址表):**

  * `id` (Serial, Primary Key)
  * `customer_id` (Integer, Foreign Key to `customers.id`)
  * `recipient_name` (Text)
  * `recipient_phone` (Text)
  * `address_details` (Text)
  * `is_default` (Boolean, Default: false)
* **`orders` (订单表):**

  * `id` (Serial, Primary Key)
  * `customer_id` (Integer, Foreign Key to `customers.id`)
  * `delivery_address` (Text) - 为简化，可直接冗余存储地址文本，或关联地址表ID
  * `delivery_datetime` (Timestamp with Time Zone)
  * `total_price` (Numeric)
  * `payment_status` (Text, 'paid' or 'unpaid')
  * `notes` (Text)
  * `created_at` (Timestamp, Default: now())
* **`order_items` (订单商品关联表):**

  * `id` (Serial, Primary Key)
  * `order_id` (Integer, Foreign Key to `orders.id`)
  * `product_id` (Integer, Foreign Key to `products.id`)
  * `quantity` (Integer, Not Null)
  * `price_at_order` (Numeric) - 下单时的商品单价，防止商品价格变动影响历史订单
  * `selected_attributes` (JSONB) - 记录用户选择的自定义属性，如 `{"规格": "8寸", "口味": "巧克力"}`
