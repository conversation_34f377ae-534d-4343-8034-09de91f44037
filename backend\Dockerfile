# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 更新包索引并安装必要的包
RUN apk update && apk add --no-cache git gcc musl-dev sqlite-dev

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

# 更新包索引并安装必要的包
RUN apk update && apk add --no-cache ca-certificates sqlite

# 设置工作目录
WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 创建数据目录
RUN mkdir -p data uploads

# 复制配置文件
COPY .env.example .env

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]
