-- 数据验证和修复脚本
-- 用于检查和修复数据完整性问题

-- 1. 检查客户数据完整性
SELECT 
    'customers' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN name IS NULL OR name = '' THEN 1 END) as missing_names,
    COUNT(CASE WHEN contact IS NULL OR contact = '' THEN 1 END) as missing_contacts
FROM customers;

-- 2. 检查地址数据完整性
SELECT 
    'addresses' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN recipient_name IS NULL OR recipient_name = '' THEN 1 END) as missing_recipient_names,
    COUNT(CASE WHEN recipient_phone IS NULL OR recipient_phone = '' THEN 1 END) as missing_phones,
    COUNT(CASE WHEN address_details IS NULL OR address_details = '' THEN 1 END) as missing_details,
    COUNT(CASE WHEN is_default = true THEN 1 END) as default_addresses
FROM addresses;

-- 3. 检查每个客户的默认地址情况
WITH customer_default_addresses AS (
    SELECT 
        c.id as customer_id,
        c.name as customer_name,
        COUNT(a.id) as total_addresses,
        COUNT(CASE WHEN a.is_default = true THEN 1 END) as default_addresses
    FROM customers c
    LEFT JOIN addresses a ON c.id = a.customer_id
    GROUP BY c.id, c.name
)
SELECT 
    'default_address_check' as check_name,
    COUNT(*) as total_customers,
    COUNT(CASE WHEN total_addresses = 0 THEN 1 END) as customers_without_addresses,
    COUNT(CASE WHEN default_addresses = 0 AND total_addresses > 0 THEN 1 END) as customers_without_default,
    COUNT(CASE WHEN default_addresses > 1 THEN 1 END) as customers_with_multiple_defaults
FROM customer_default_addresses;

-- 4. 检查商品数据完整性
SELECT 
    'products' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN name IS NULL OR name = '' THEN 1 END) as missing_names,
    COUNT(CASE WHEN price IS NULL OR price <= 0 THEN 1 END) as invalid_prices,
    COUNT(CASE WHEN stock_quantity IS NULL THEN 1 END) as missing_stock,
    COUNT(CASE WHEN stock_quantity <= min_stock_level THEN 1 END) as low_stock_products
FROM products;

-- 5. 检查订单数据完整性
SELECT 
    'orders' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_id IS NULL THEN 1 END) as missing_customers,
    COUNT(CASE WHEN total_price IS NULL OR total_price < 0 THEN 1 END) as invalid_prices,
    COUNT(CASE WHEN delivery_address IS NULL OR delivery_address = '' THEN 1 END) as missing_addresses,
    COUNT(CASE WHEN order_status NOT IN ('pending', 'production', 'delivery', 'completed', 'cancelled') THEN 1 END) as invalid_status
FROM orders;

-- 6. 检查订单项数据完整性
SELECT 
    'order_items' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN order_id IS NULL THEN 1 END) as missing_order_id,
    COUNT(CASE WHEN product_id IS NULL THEN 1 END) as missing_product_id,
    COUNT(CASE WHEN quantity IS NULL OR quantity <= 0 THEN 1 END) as invalid_quantities,
    COUNT(CASE WHEN price_at_order IS NULL OR price_at_order < 0 THEN 1 END) as invalid_prices
FROM order_items;

-- 7. 检查孤儿记录
-- 检查没有对应客户的地址
SELECT 
    'orphan_addresses' as check_name,
    COUNT(*) as count
FROM addresses a
LEFT JOIN customers c ON a.customer_id = c.id
WHERE c.id IS NULL;

-- 检查没有对应客户的订单
SELECT 
    'orphan_orders' as check_name,
    COUNT(*) as count
FROM orders o
LEFT JOIN customers c ON o.customer_id = c.id
WHERE c.id IS NULL;

-- 检查没有对应订单的订单项
SELECT 
    'orphan_order_items' as check_name,
    COUNT(*) as count
FROM order_items oi
LEFT JOIN orders o ON oi.order_id = o.id
WHERE o.id IS NULL;

-- 检查没有对应商品的订单项
SELECT 
    'orphan_order_items_products' as check_name,
    COUNT(*) as count
FROM order_items oi
LEFT JOIN products p ON oi.product_id = p.id
WHERE p.id IS NULL;

-- 8. 数据修复建议查询
-- 找出需要设置默认地址的客户
SELECT 
    c.id as customer_id,
    c.name as customer_name,
    'needs_default_address' as issue,
    'Customer has addresses but no default address set' as description
FROM customers c
WHERE EXISTS (
    SELECT 1 FROM addresses a WHERE a.customer_id = c.id
) AND NOT EXISTS (
    SELECT 1 FROM addresses a WHERE a.customer_id = c.id AND a.is_default = true
);

-- 找出有多个默认地址的客户
SELECT 
    c.id as customer_id,
    c.name as customer_name,
    COUNT(a.id) as default_address_count,
    'multiple_default_addresses' as issue,
    'Customer has multiple default addresses' as description
FROM customers c
JOIN addresses a ON c.id = a.customer_id
WHERE a.is_default = true
GROUP BY c.id, c.name
HAVING COUNT(a.id) > 1;

-- 找出价格异常的商品
SELECT 
    id,
    name,
    price,
    'invalid_price' as issue,
    'Product has invalid price' as description
FROM products
WHERE price IS NULL OR price <= 0;

-- 找出库存异常的商品
SELECT 
    id,
    name,
    stock_quantity,
    min_stock_level,
    'negative_stock' as issue,
    'Product has negative stock' as description
FROM products
WHERE stock_quantity < 0;

-- 9. 生成修复脚本
-- 修复没有默认地址的客户（设置第一个地址为默认）
SELECT 
    'UPDATE addresses SET is_default = true WHERE id = ' || a.id || ';' as fix_sql
FROM (
    SELECT DISTINCT ON (customer_id) 
        id, customer_id
    FROM addresses
    WHERE customer_id IN (
        SELECT c.id 
        FROM customers c
        WHERE EXISTS (
            SELECT 1 FROM addresses a WHERE a.customer_id = c.id
        ) AND NOT EXISTS (
            SELECT 1 FROM addresses a WHERE a.customer_id = c.id AND a.is_default = true
        )
    )
    ORDER BY customer_id, created_at ASC
) a;

-- 修复多个默认地址的问题（保留最新的，其他设为非默认）
SELECT 
    'UPDATE addresses SET is_default = false WHERE id = ' || a.id || ';' as fix_sql
FROM addresses a
WHERE a.is_default = true
AND a.id NOT IN (
    SELECT DISTINCT ON (customer_id) id
    FROM addresses
    WHERE is_default = true
    ORDER BY customer_id, created_at DESC
);

-- 10. 数据统计报告
SELECT 
    'DATA_INTEGRITY_REPORT' as report_type,
    NOW() as generated_at;

-- 客户统计
SELECT 
    'CUSTOMERS' as category,
    COUNT(*) as total,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as last_30_days,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as last_7_days
FROM customers;

-- 地址统计
SELECT 
    'ADDRESSES' as category,
    COUNT(*) as total,
    COUNT(CASE WHEN is_default = true THEN 1 END) as default_addresses,
    ROUND(AVG(address_count), 2) as avg_addresses_per_customer
FROM addresses a
JOIN (
    SELECT customer_id, COUNT(*) as address_count
    FROM addresses
    GROUP BY customer_id
) ac ON a.customer_id = ac.customer_id;

-- 商品统计
SELECT 
    'PRODUCTS' as category,
    COUNT(*) as total,
    COUNT(CASE WHEN is_listed = true THEN 1 END) as listed,
    COUNT(CASE WHEN stock_quantity <= min_stock_level THEN 1 END) as low_stock,
    COUNT(CASE WHEN stock_quantity = 0 THEN 1 END) as out_of_stock
FROM products;

-- 订单统计
SELECT 
    'ORDERS' as category,
    COUNT(*) as total,
    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed,
    COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid,
    ROUND(AVG(total_price), 2) as avg_order_value
FROM orders;
