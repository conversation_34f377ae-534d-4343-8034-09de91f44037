-- 示例数据插入脚本
-- 用于开发和测试环境

-- 插入示例商品数据
INSERT INTO products (name, category, price, is_listed, custom_attributes) VALUES
('巧克力生日蛋糕', '蛋糕', 128.00, true, '{"规格": ["6寸", "8寸", "10寸"], "口味": ["巧克力", "香草"]}'),
('草莓奶油蛋糕', '蛋糕', 138.00, true, '{"规格": ["6寸", "8寸", "10寸"], "口味": ["草莓", "香草"]}'),
('芝士蛋糕', '蛋糕', 158.00, true, '{"规格": ["6寸", "8寸"], "口味": ["原味", "蓝莓"]}'),
('曲奇饼干礼盒', '饼干', 68.00, true, '{"包装": ["小盒", "大盒"], "口味": ["原味", "巧克力", "抹茶"]}'),
('手工牛轧糖', '糖果', 38.00, true, '{"包装": ["袋装", "盒装"], "口味": ["原味", "花生", "蔓越莓"]}'),
('法式马卡龙', '甜点', 88.00, true, '{"数量": ["6个装", "12个装"], "口味": ["混合", "单一口味"]}'),
('提拉米苏', '甜点', 48.00, false, '{"规格": ["单人份", "双人份"]}'),
('全麦面包', '面包', 18.00, true, '{"重量": ["500g", "1000g"]}');

-- 插入示例客户数据
INSERT INTO customers (name, contact) VALUES
('张三', '13800138001'),
('李四', '13800138002'),
('王五', '13800138003'),
('赵六', 'wechat:zhaoliu123'),
('孙七', '13800138004'),
('周八', '13800138005');

-- 插入示例地址数据
INSERT INTO addresses (customer_id, recipient_name, recipient_phone, address_details, is_default) VALUES
(1, '张三', '13800138001', '北京市朝阳区建国路88号SOHO现代城A座1001', true),
(1, '张三', '13800138001', '北京市海淀区中关村大街1号', false),
(2, '李四', '13800138002', '上海市浦东新区陆家嘴环路1000号', true),
(3, '王五', '13800138003', '广州市天河区珠江新城花城大道85号', true),
(4, '赵六', '13800138006', '深圳市南山区科技园南区深南大道9988号', true),
(5, '孙七', '13800138004', '杭州市西湖区文三路90号', true),
(6, '周八', '13800138005', '成都市锦江区春熙路99号', true);

-- 插入示例订单数据
INSERT INTO orders (customer_id, delivery_address, delivery_datetime, total_price, payment_status, order_status, notes) VALUES
(1, '北京市朝阳区建国路88号SOHO现代城A座1001', '2024-06-20 14:00:00+08', 256.00, 'paid', 'completed', '生日蛋糕，请写上"生日快乐"'),
(2, '上海市浦东新区陆家嘴环路1000号', '2024-06-21 16:30:00+08', 138.00, 'paid', 'delivery', ''),
(3, '广州市天河区珠江新城花城大道85号', '2024-06-22 10:00:00+08', 206.00, 'unpaid', 'pending', '需要无糖版本'),
(4, '深圳市南山区科技园南区深南大道9988号', '2024-06-23 15:00:00+08', 156.00, 'paid', 'production', '公司团建用'),
(5, '杭州市西湖区文三路90号', '2024-06-24 11:30:00+08', 88.00, 'unpaid', 'pending', ''),
(1, '北京市海淀区中关村大街1号', '2024-06-25 09:00:00+08', 68.00, 'paid', 'production', '办公室下午茶');

-- 插入示例订单商品关联数据
INSERT INTO order_items (order_id, product_id, quantity, price_at_order, selected_attributes) VALUES
-- 订单1：张三的生日蛋糕订单
(1, 1, 2, 128.00, '{"规格": "8寸", "口味": "巧克力"}'),
-- 订单2：李四的草莓蛋糕订单
(2, 2, 1, 138.00, '{"规格": "8寸", "口味": "草莓"}'),
-- 订单3：王五的混合订单
(3, 3, 1, 158.00, '{"规格": "6寸", "口味": "蓝莓"}'),
(3, 4, 1, 68.00, '{"包装": "小盒", "口味": "巧克力"}'),
-- 订单4：赵六的马卡龙和牛轧糖
(4, 6, 1, 88.00, '{"数量": "12个装", "口味": "混合"}'),
(4, 5, 2, 38.00, '{"包装": "盒装", "口味": "花生"}'),
-- 订单5：孙七的马卡龙
(5, 6, 1, 88.00, '{"数量": "6个装", "口味": "混合"}'),
-- 订单6：张三的饼干
(6, 4, 1, 68.00, '{"包装": "大盒", "口味": "原味"}');

-- 注意：user_profiles表的数据需要在用户注册后通过触发器自动创建
-- 或者在Supabase控制台中手动创建超级管理员账户后插入
