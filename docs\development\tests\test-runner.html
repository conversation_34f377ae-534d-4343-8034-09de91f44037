<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小商店订单管理系统 - API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .status.running {
            color: #007bff;
        }
        
        .status.success {
            color: #28a745;
        }
        
        .status.error {
            color: #dc3545;
        }
        
        .results {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .test-output {
            background: #000;
            color: #00ff00;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .summary-card.passed {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .summary-card.failed {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .summary-card p {
            margin: 0;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .api-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .api-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .api-card.online {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .api-card.offline {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .api-card h4 {
            margin: 0 0 10px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #28a745;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 小商店订单管理系统 - API功能测试</h1>
        
        <div class="api-status">
            <div class="api-card" id="frontend-status">
                <h4><span class="status-indicator" id="frontend-indicator"></span>前端服务</h4>
                <p id="frontend-url">http://localhost:5173</p>
            </div>
            <div class="api-card" id="backend-status">
                <h4><span class="status-indicator" id="backend-indicator"></span>后端API</h4>
                <p id="backend-url">http://localhost:8080</p>
            </div>
        </div>
        
        <div class="test-controls">
            <button onclick="checkServices()" id="check-btn">检查服务状态</button>
            <button onclick="runTests()" id="test-btn">开始测试</button>
            <button onclick="clearOutput()" id="clear-btn">清空输出</button>
        </div>
        
        <div class="status" id="status">准备就绪</div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <div class="summary">
                <div class="summary-card">
                    <h3 id="total-tests">0</h3>
                    <p>总测试数</p>
                </div>
                <div class="summary-card passed">
                    <h3 id="passed-tests">0</h3>
                    <p>通过</p>
                </div>
                <div class="summary-card failed">
                    <h3 id="failed-tests">0</h3>
                    <p>失败</p>
                </div>
                <div class="summary-card">
                    <h3 id="success-rate">0%</h3>
                    <p>成功率</p>
                </div>
            </div>
        </div>
        
        <div class="test-output" id="output"></div>
    </div>

    <script>
        let testResults = { passed: 0, failed: 0, total: 0 };
        let authToken = '';
        
        // 检查服务状态
        async function checkServices() {
            const checkBtn = document.getElementById('check-btn');
            checkBtn.disabled = true;
            checkBtn.textContent = '检查中...';
            
            // 检查前端
            try {
                const frontendResponse = await fetch('http://localhost:5173');
                updateServiceStatus('frontend', true);
            } catch (error) {
                updateServiceStatus('frontend', false);
            }
            
            // 检查后端
            try {
                const backendResponse = await fetch('http://localhost:8080/health');
                updateServiceStatus('backend', backendResponse.ok);
            } catch (error) {
                updateServiceStatus('backend', false);
            }
            
            checkBtn.disabled = false;
            checkBtn.textContent = '检查服务状态';
        }
        
        function updateServiceStatus(service, isOnline) {
            const card = document.getElementById(`${service}-status`);
            const indicator = document.getElementById(`${service}-indicator`);
            
            if (isOnline) {
                card.className = 'api-card online';
                indicator.className = 'status-indicator online';
            } else {
                card.className = 'api-card offline';
                indicator.className = 'status-indicator offline';
            }
        }
        
        // 输出日志
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        // 更新状态
        function updateStatus(message, type = '') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 更新进度
        function updateProgress(current, total) {
            const progress = document.getElementById('progress');
            const percentage = total > 0 ? (current / total) * 100 : 0;
            progress.style.width = `${percentage}%`;
        }
        
        // 更新结果统计
        function updateResults() {
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? 
                ((testResults.passed / testResults.total) * 100).toFixed(1) : 0;
            document.getElementById('success-rate').textContent = `${successRate}%`;
            
            document.getElementById('results').style.display = 'block';
        }
        
        // API请求函数
        async function apiRequest(endpoint, options = {}) {
            const url = `http://localhost:8080/api${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 运行单个测试
        async function runTest(testName, testFn) {
            testResults.total++;
            log(`\n🧪 测试: ${testName}`);
            updateProgress(testResults.passed + testResults.failed, testResults.total);
            
            try {
                const result = await testFn();
                if (result) {
                    log(`✅ 通过: ${testName}`);
                    testResults.passed++;
                } else {
                    log(`❌ 失败: ${testName}`);
                    testResults.failed++;
                }
            } catch (error) {
                log(`❌ 失败: ${testName} - ${error.message}`);
                testResults.failed++;
            }
            
            updateResults();
        }
        
        // 测试函数（从api-test.js复制并修改）
        async function testAuth() {
            const loginResult = await apiRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'admin123'
                })
            });

            if (loginResult.success && loginResult.data.token) {
                authToken = loginResult.data.token;
                log('  ✓ 登录成功');
                
                const profileResult = await apiRequest('/user/profile');
                if (profileResult.success) {
                    log('  ✓ 获取用户信息成功');
                    return true;
                }
            }
            
            log('  ✗ 认证测试失败');
            return false;
        }
        
        async function testScheduler() {
            const statusResult = await apiRequest('/scheduler/status');
            if (!statusResult.success) {
                log('  ✗ 获取定时任务状态失败');
                return false;
            }
            log('  ✓ 获取定时任务状态成功');

            const jobsResult = await apiRequest('/scheduler/jobs');
            if (!jobsResult.success) {
                log('  ✗ 获取任务列表失败');
                return false;
            }
            log(`  ✓ 获取任务列表成功 (${jobsResult.data.length} 个任务)`);
            
            return true;
        }
        
        async function testHolidays() {
            const listResult = await apiRequest('/holidays?year=2025');
            if (!listResult.success) {
                log('  ✗ 获取节假日列表失败');
                return false;
            }
            log(`  ✓ 获取节假日列表成功 (${listResult.data.length} 条记录)`);

            const lunarResult = await apiRequest('/calendar/lunar?date=2025-06-17');
            if (!lunarResult.success) {
                log('  ✗ 农历转换失败');
                return false;
            }
            log('  ✓ 农历转换成功');
            
            return true;
        }
        
        // 主测试函数
        async function runTests() {
            const testBtn = document.getElementById('test-btn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            // 重置结果
            testResults = { passed: 0, failed: 0, total: 0 };
            authToken = '';
            
            updateStatus('测试进行中...', 'running');
            log('🚀 开始API功能测试...\n');
            
            await runTest('用户认证', testAuth);
            await runTest('定时任务管理', testScheduler);
            await runTest('节假日管理', testHolidays);
            
            log('\n📊 测试结果统计:');
            log(`总计: ${testResults.total}`);
            log(`通过: ${testResults.passed}`);
            log(`失败: ${testResults.failed}`);
            log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
            
            if (testResults.failed === 0) {
                log('\n🎉 所有测试通过！');
                updateStatus('所有测试通过！', 'success');
            } else {
                log('\n⚠️  部分测试失败，请检查相关功能');
                updateStatus('部分测试失败', 'error');
            }
            
            updateProgress(testResults.total, testResults.total);
            
            testBtn.disabled = false;
            testBtn.textContent = '开始测试';
        }
        
        // 清空输出
        function clearOutput() {
            document.getElementById('output').textContent = '';
            document.getElementById('results').style.display = 'none';
            document.getElementById('progress').style.width = '0%';
            updateStatus('准备就绪');
        }
        
        // 页面加载时检查服务状态
        window.addEventListener('load', checkServices);
    </script>
</body>
</html>
