package controllers

import (
	"net/http"
	"strconv"
	"time"

	"shop-order-backend/internal/models"
	"shop-order-backend/internal/services"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type HolidayController struct {
	holidayService *services.HolidayService
}

func NewHolidayController(db *xorm.Engine) *HolidayController {
	return &HolidayController{
		holidayService: services.NewHolidayService(db),
	}
}

// GetHolidays 获取节假日列表
func (hc *HolidayController) GetHolidays(c *gin.Context) {
	yearStr := c.Query("year")
	if yearStr == "" {
		yearStr = strconv.Itoa(time.Now().Year())
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid year parameter",
		})
		return
	}

	holidays, err := hc.holidayService.GetHolidays(year)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holidays",
		})
		return
	}

	// 转换为响应格式
	var responses []*models.HolidayResponse
	for _, holiday := range holidays {
		responses = append(responses, holiday.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
		"year": year,
	})
}

// GetHolidayByDate 获取指定日期的节假日信息
func (hc *HolidayController) GetHolidayByDate(c *gin.Context) {
	dateStr := c.Param("date")
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid date format, use YYYY-MM-DD",
		})
		return
	}

	holiday, err := hc.holidayService.GetHolidayByDate(date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holiday",
		})
		return
	}

	if holiday == nil {
		c.JSON(http.StatusOK, gin.H{
			"data":       nil,
			"is_holiday": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       holiday.ToResponse(),
		"is_holiday": holiday.Type == "holiday",
	})
}

// SyncHolidays 同步节假日数据
func (hc *HolidayController) SyncHolidays(c *gin.Context) {
	var req struct {
		Year int `json:"year"`
	}

	// 尝试从请求体获取年份
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果请求体解析失败，尝试从查询参数获取
		yearStr := c.Query("year")
		if yearStr == "" {
			yearStr = strconv.Itoa(time.Now().Year())
		}

		year, parseErr := strconv.Atoi(yearStr)
		if parseErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid year parameter",
			})
			return
		}
		req.Year = year
	}

	// 如果年份为0，使用当前年份
	if req.Year == 0 {
		req.Year = time.Now().Year()
	}

	err := hc.holidayService.SyncHolidays(req.Year)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to sync holidays: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Holidays synced successfully",
		"year":    req.Year,
	})
}

// CreateHoliday 创建自定义节假日
func (hc *HolidayController) CreateHoliday(c *gin.Context) {
	var req models.HolidayRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	holiday := &models.Holiday{
		Date:       req.Date,
		Name:       req.Name,
		Type:       req.Type,
		IsOfficial: req.IsOfficial,
		Year:       req.Date.Year(),
	}

	err := hc.holidayService.CreateHoliday(holiday)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create holiday",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"data": holiday.ToResponse(),
	})
}

// UpdateHoliday 更新节假日
func (hc *HolidayController) UpdateHoliday(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid holiday ID",
		})
		return
	}

	var req models.HolidayRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	updates := &models.Holiday{
		Date:       req.Date,
		Name:       req.Name,
		Type:       req.Type,
		IsOfficial: req.IsOfficial,
	}

	err = hc.holidayService.UpdateHoliday(id, updates)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update holiday",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Holiday updated successfully",
	})
}

// DeleteHoliday 删除节假日
func (hc *HolidayController) DeleteHoliday(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid holiday ID",
		})
		return
	}

	err = hc.holidayService.DeleteHoliday(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete holiday",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Holiday deleted successfully",
	})
}

// GetHolidaysInRange 获取日期范围内的节假日
func (hc *HolidayController) GetHolidaysInRange(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "start_date and end_date are required",
		})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid start_date format, use YYYY-MM-DD",
		})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid end_date format, use YYYY-MM-DD",
		})
		return
	}

	holidays, err := hc.holidayService.GetHolidaysInRange(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holidays",
		})
		return
	}

	// 转换为响应格式
	var responses []*models.HolidayResponse
	for _, holiday := range holidays {
		responses = append(responses, holiday.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       responses,
		"start_date": startDateStr,
		"end_date":   endDateStr,
	})
}
