@echo off
echo ========================================
echo Project Health Check
echo ========================================
echo.

set errors=0

echo Checking project structure...
if not exist "backend" (
    echo ERROR: Backend directory not found
    set /a errors+=1
)
if not exist "shop-order-system" (
    echo ERROR: Frontend directory not found
    set /a errors+=1
)
if not exist "docs" (
    echo ERROR: Documentation directory not found
    set /a errors+=1
)

echo Checking key files...
if not exist "README.md" (
    echo ERROR: README.md not found
    set /a errors+=1
)
if not exist "LICENSE" (
    echo ERROR: LICENSE not found
    set /a errors+=1
)
if not exist "docker-compose.yml" (
    echo ERROR: docker-compose.yml not found
    set /a errors+=1
)

echo.
if %errors%==0 (
    echo SUCCESS: All checks passed!
    echo Project is ready for production.
) else (
    echo FAILED: Found %errors% errors.
    echo Please fix the issues above.
)

echo.
pause
