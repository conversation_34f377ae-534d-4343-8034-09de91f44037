# 更新日志

本文档记录了小商店订单管理系统的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划新增
- 移动端适配优化
- 数据导出功能
- 高级报表功能
- 微信小程序支持

## [1.0.0] - 2025-06-17

### 新增
- 🎉 **首次发布** - 小商店订单管理系统正式版本
- 🛍️ **商品管理** - 完整的商品CRUD功能
  - 商品信息管理
  - 库存管理
  - 分类管理
  - 自定义属性
- 👥 **客户管理** - 客户档案管理
  - 客户信息维护
  - 多地址管理
  - 默认地址设置
  - 联系方式管理
- 📋 **订单管理** - 完整的订单处理流程
  - 三步式订单创建
  - 订单状态跟踪
  - 支付状态管理
  - 订单搜索筛选
- 📅 **生产日历** - 生产计划管理
  - 日历视图
  - 订单排程
  - 工作负载显示
- 👤 **用户管理** - 用户认证和权限
  - JWT身份认证
  - 角色权限控制
  - 用户档案管理
- ⚙️ **定时任务** - 系统自动化
  - 任务调度
  - 系统维护
  - 数据同步
- 📊 **数据统计** - 业务分析
  - 销售统计
  - 订单分析
  - 图表展示

### 技术特性
- 🚀 **现代化技术栈**
  - 前端：Vue 3 + Element Plus + Pinia
  - 后端：Go + Gin + XORM
  - 数据库：SQLite
- 🐳 **容器化部署** - Docker支持
- 📱 **响应式设计** - 移动端适配
- 🔒 **安全认证** - JWT令牌认证
- 🌐 **RESTful API** - 标准化接口设计

### 修复
- 🐛 **商品选择功能** - 修复Vue 3响应式循环更新错误
- 🐛 **库存控件布局** - 优化CSS布局，解决控件重叠问题
- 🐛 **地址管理** - 修复设置默认地址500错误
- 🐛 **订单创建** - 修复API调用问题，确保订单创建成功

### 优化
- ⚡ **性能优化** - 前端组件渲染优化
- 🎨 **用户体验** - 界面交互优化
- 📝 **代码质量** - 代码规范和注释完善
- 🧪 **测试覆盖** - 添加测试用例

### 文档
- 📚 **完整文档** - 用户指南、开发文档、部署文档
- 🔧 **API文档** - 详细的接口文档
- 🏗️ **架构文档** - 系统架构和数据库设计
- 📖 **使用手册** - 功能使用说明

## [0.9.0] - 2025-06-16

### 新增
- 基础订单管理功能
- 客户信息管理
- 商品基础功能

### 修复
- 初始版本bug修复

## [0.8.0] - 2025-06-15

### 新增
- 项目初始化
- 基础架构搭建
- 用户认证系统

---

## 版本说明

### 版本号格式
本项目使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- `新增` - 新功能
- `修改` - 对现有功能的变更
- `弃用` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关修复

### 获取更新
- 查看 [Releases](../../releases) 获取最新版本
- 关注 [Issues](../../issues) 了解开发进度
- 参与 [Discussions](../../discussions) 讨论新功能

---

**📝 注意：此更新日志会随着每个版本的发布而更新，建议定期查看以了解最新变更。**
