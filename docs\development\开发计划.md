# 小商店订单管理系统 - 功能完善开发计划

## 📋 项目概述

基于当前系统的功能完成度评估（95%），本开发计划旨在完善剩余的核心功能，提升系统的实用性和用户体验。

## 🎯 开发目标

1. **日历功能增强** - 支持节假日同步、农历显示、订单商品汇总
2. **客户地址管理优化** - 解决客户和地址管理的割裂问题
3. **订单管理完善** - 建立订单商品关系、自动价格计算、客户搜索优化
4. **用户体验提升** - 优化界面交互，提升操作效率

## 📅 开发周期

**预计总工期：3-4周**
- 第1周：后端API扩展 + 日历功能增强
- 第2周：订单管理核心功能完善
- 第3周：客户地址管理优化 + 前端UI/UX优化
- 第4周：测试、优化和部署

## 🚀 详细开发计划

### 阶段一：后端API扩展 (第1周前半)

#### 1.1 节假日API接口
- **目标**：集成第三方节假日API，实现数据获取和存储
- **技术方案**：
  - 集成国家政务服务平台节假日API
  - 创建 `holidays` 数据表
  - 实现定时任务自动同步
- **API端点**：
  ```
  GET /api/holidays?year=2024
  POST /api/holidays/sync
  ```

#### 1.2 农历转换API
- **目标**：实现公历转农历的算法和API
- **技术方案**：
  - 集成农历计算库
  - 缓存常用转换结果
- **API端点**：
  ```
  GET /api/calendar/lunar?date=2024-01-01
  ```

#### 1.3 订单统计API
- **目标**：支持按日期、商品分类的订单统计
- **技术方案**：
  - 优化数据库查询性能
  - 支持多维度统计
- **API端点**：
  ```
  GET /api/orders/statistics?date=2024-01-01&groupBy=category
  ```

#### 1.4 客户搜索API优化
- **目标**：支持模糊搜索和分页
- **技术方案**：
  - 实现全文搜索
  - 添加索引优化性能
- **API端点**：
  ```
  GET /api/customers/search?q=张三&page=1&limit=20
  ```

### 阶段二：日历功能增强 (第1周后半)

#### 2.1 节假日数据模型
```sql
CREATE TABLE holidays (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'holiday', 'workday'
    is_official BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 2.2 农历计算功能
- 集成 `lunar-javascript` 或类似库
- 实现公历转农历算法
- 支持节气、生肖等信息

#### 2.3 订单商品汇总算法
- 按日期统计订单中各商品分类
- 支持数量和金额汇总
- 优化显示逻辑（超出显示"更多"）

#### 2.4 日历组件UI优化
- 节假日标记（红色背景）
- 农历显示（小字体）
- 订单汇总显示（标签形式）
- 点击详情弹窗

### 阶段三：订单管理核心功能完善 (第2周)

#### 3.1 订单商品关系模型修复
- **问题分析**：当前 order_items 表可能存在关联问题
- **解决方案**：
  ```sql
  -- 确保外键约束正确
  ALTER TABLE order_items 
  ADD CONSTRAINT fk_order_items_order 
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;
  
  ALTER TABLE order_items 
  ADD CONSTRAINT fk_order_items_product 
  FOREIGN KEY (product_id) REFERENCES products(id);
  ```

#### 3.2 价格自动计算功能
- **前端实现**：
  - 监听商品选择和数量变化
  - 实时计算总价
  - 支持折扣和优惠
- **后端验证**：
  - 服务端价格验证
  - 防止价格篡改

#### 3.3 智能客户搜索组件
```vue
<template>
  <el-select
    v-model="selectedCustomer"
    filterable
    remote
    reserve-keyword
    placeholder="搜索客户（姓名/手机号）"
    :remote-method="searchCustomers"
    :loading="loading"
  >
    <el-option
      v-for="customer in customerOptions"
      :key="customer.id"
      :label="`${customer.name} (${customer.contact})`"
      :value="customer.id"
    />
  </el-select>
</template>
```

#### 3.4 快速添加客户功能
- 在客户选择旁边添加"+"按钮
- 弹出简化的客户创建表单
- 创建成功后自动选中新客户

### 阶段四：客户地址管理优化 (第3周前半)

#### 4.1 客户详情页面重设计
```vue
<template>
  <div class="customer-detail">
    <!-- 客户基本信息 -->
    <el-card class="customer-info">
      <h3>客户信息</h3>
      <!-- 客户表单 -->
    </el-card>
    
    <!-- 地址管理 -->
    <el-card class="address-management">
      <div class="address-header">
        <h3>地址管理</h3>
        <el-button type="primary" @click="addAddress">添加地址</el-button>
      </div>
      <address-list :customer-id="customerId" />
    </el-card>
  </div>
</template>
```

#### 4.2 地址管理组件优化
- 支持拖拽排序
- 批量操作（删除、设为默认）
- 地址验证和格式化

#### 4.3 可复用地址选择组件
```vue
<template>
  <div class="address-selector">
    <el-select v-model="selectedAddress" placeholder="选择地址">
      <el-option
        v-for="address in addresses"
        :key="address.id"
        :label="formatAddress(address)"
        :value="address.id"
      >
        <div class="address-option">
          <div class="recipient">{{ address.recipient_name }}</div>
          <div class="address">{{ address.address_details }}</div>
          <el-tag v-if="address.is_default" size="small">默认</el-tag>
        </div>
      </el-option>
    </el-select>
    <el-button @click="addNewAddress">新增地址</el-button>
  </div>
</template>
```

### 阶段五：前端UI/UX优化 (第3周后半)

#### 5.1 订单表单优化
- 步骤式表单设计
- 实时验证和提示
- 自动保存草稿

#### 5.2 商品选择组件优化
- 支持图片预览
- 分类筛选
- 属性选择器

#### 5.3 移动端适配增强
- 触摸友好的交互
- 响应式布局优化
- 手势操作支持

### 阶段六：测试和部署 (第4周)

#### 6.1 功能测试
- 单元测试覆盖
- 集成测试
- 用户验收测试

#### 6.2 性能优化
- 数据库查询优化
- 前端资源优化
- 缓存策略

#### 6.3 部署优化
- Docker镜像优化
- 监控和日志
- 备份策略

## 🛠️ 技术实现要点

### 节假日API集成
```go
// 节假日服务
type HolidayService struct {
    db *xorm.Engine
}

func (s *HolidayService) SyncHolidays(year int) error {
    // 调用第三方API获取节假日数据
    holidays, err := s.fetchHolidaysFromAPI(year)
    if err != nil {
        return err
    }
    
    // 批量插入数据库
    return s.db.Insert(holidays)
}
```

### 农历转换
```javascript
// 农历转换工具
import { Lunar } from 'lunar-javascript'

export function solarToLunar(date) {
    const solar = Solar.fromDate(date)
    const lunar = solar.getLunar()
    return {
        year: lunar.getYear(),
        month: lunar.getMonth(),
        day: lunar.getDay(),
        monthCn: lunar.getMonthInChinese(),
        dayCn: lunar.getDayInChinese()
    }
}
```

### 订单统计查询
```sql
-- 按日期和分类统计订单
SELECT 
    DATE(o.delivery_datetime) as order_date,
    p.category,
    COUNT(oi.id) as item_count,
    SUM(oi.quantity) as total_quantity,
    SUM(oi.quantity * oi.price_at_order) as total_amount
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
JOIN products p ON oi.product_id = p.id
WHERE DATE(o.delivery_datetime) BETWEEN ? AND ?
GROUP BY DATE(o.delivery_datetime), p.category
ORDER BY order_date, p.category;
```

## 📊 预期成果

1. **功能完整性**：达到100%功能覆盖
2. **用户体验**：操作效率提升50%
3. **系统性能**：响应时间优化30%
4. **代码质量**：测试覆盖率达到80%

## 🔄 迭代计划

### 版本 2.1.0 (第1-2周)
- 日历功能增强
- 订单管理核心功能

### 版本 2.2.0 (第3周)
- 客户地址管理优化
- UI/UX改进

### 版本 2.3.0 (第4周)
- 性能优化
- 测试完善
- 部署优化

## 📝 注意事项

1. **数据迁移**：确保现有数据的完整性
2. **向下兼容**：保持API的向下兼容性
3. **性能监控**：关注系统性能指标
4. **用户反馈**：及时收集和处理用户反馈

## 🎯 成功标准

- [ ] 所有功能需求100%实现
- [ ] 用户验收测试通过
- [ ] 性能指标达标
- [ ] 代码质量检查通过
- [ ] 部署文档完善
